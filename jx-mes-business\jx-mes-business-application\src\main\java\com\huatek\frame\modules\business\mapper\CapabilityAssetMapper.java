package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;

import com.huatek.frame.modules.business.service.dto.CapabilityVerificationCheckDTO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.MapKey;
import java.util.Map;
import com.huatek.frame.modules.business.domain.CapabilityAsset;
import  com.huatek.frame.modules.business.domain.vo.CapabilityAssetVO;
import com.huatek.frame.modules.business.service.dto.CapabilityAssetDTO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

/**
* 能力资产mapper
* <AUTHOR>
* @date 2025-08-04
**/
public interface CapabilityAssetMapper extends BaseMapper<CapabilityAsset> {

     /**
	 * 能力资产分页
	 * @param dto
	 * @return
	 */
	Page<CapabilityAssetVO> selectCapabilityAssetPage(CapabilityAssetDTO dto);

    /**
	 * 外键关联表: product_management - product_model
     **/
    @ApiModelProperty("外键 product_management - product_model")
	Page<SelectOptionsVO> selectOptionsByProductModel(String productModel);
    Map<String,String> selectDataLinkageByProductModel(@Param("product_model") String product_model);
    /**
	 * 外键关联表: capability_development - task_number
     **/
    @ApiModelProperty("外键 capability_development - task_number")
	Page<SelectOptionsVO> selectOptionsByTaskNumber(String taskNumber);
    Map<String,String> selectDataLinkageByTaskNumber(@Param("task_number") String task_number);

    /**
     * 根据条件查询能力资产列表
     *
     * @param dto 能力资产信息
     * @return 能力资产集合信息
     */
    List<CapabilityAssetVO> selectCapabilityAssetList(CapabilityAssetDTO dto);

	/**
	 * 根据IDS查询能力资产列表
	 * @param ids
	 * @return
	 */
    List<CapabilityAssetVO> selectCapabilityAssetListByIds(@Param("ids") List<String> ids);
    
    /**
     * 根据多个产品型号和能力类型组合查询能力资产列表
     *
     * @param queryParams 查询参数列表，包含产品型号和能力类型组合
     * @return 能力资产集合信息
     */
    List<CapabilityAssetVO> selectCapabilityAssetListByMultiParams(@Param("queryParams") List<CapabilityVerificationCheckDTO> queryParams);

    /**
     * 根据产品型号和能力类型查询能力资产
     * @param productModel 产品型号
     * @param capabilityType 能力类型
     * @return 能力资产列表
     */
    List<CapabilityAssetVO> selectByProductModelAndType(
            @Param("productModel") String productModel,
            @Param("capabilityType") String capabilityType);

}