package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import com.huatek.frame.modules.business.domain.EvaluationOrderNotify;
import com.huatek.frame.modules.business.domain.vo.EvaluationOrderNotifyVO;
import com.huatek.frame.modules.business.service.dto.EvaluationOrderNotifyDTO;
import org.springframework.stereotype.Component;

@Component
public interface EvaluationOrderNotifyMapper extends BaseMapper<EvaluationOrderNotify> {

    /**
     * 分页查询测评订单通知信息
     * @param requestParam
     * @return
     */
    Page<EvaluationOrderNotifyVO> findAll(EvaluationOrderNotifyDTO requestParam);
}
