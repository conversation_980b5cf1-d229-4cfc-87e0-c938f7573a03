package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import java.util.Map;
import com.huatek.frame.modules.business.domain.ExperimentProject;
import  com.huatek.frame.modules.business.domain.vo.ExperimentProjectVO;
import com.huatek.frame.modules.business.service.dto.ExperimentProjectDTO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

/**
* 试验项目mapper
* <AUTHOR>
* @date 2025-07-17
**/
public interface ExperimentProjectMapper extends BaseMapper<ExperimentProject> {

     /**
	 * 试验项目分页
	 * @param dto
	 * @return
	 */
	Page<ExperimentProjectVO> selectExperimentProjectPage(ExperimentProjectDTO dto);

	/**
	 * 外键关联表: standard_process_management - step_number
	 **/
	@ApiModelProperty("外键 standard_process_management - step_number")
	Page<SelectOptionsVO> selectOptionsByProcessId(String processId);
	Map<String,String> selectDataLinkageByProcessId(@Param("step_number") String step_number);
	/**
	 * 外键关联表: standard_process_management - step_number
	 **/
	@ApiModelProperty("外键 standard_process_management - step_number")
	Page<SelectOptionsVO> selectOptionsByAssoWoPredProc(String assoWoPredProc);
	/**
	 * 外键关联表: workstation - workstation_number
	 **/
	@ApiModelProperty("外键 workstation - workstation_number")
	Page<SelectOptionsVO> selectOptionsByWorkstation(String workstation);
	Map<String,String> selectDataLinkageByWorkstation(@Param("workstation_number") String workstation_number);
	/**
	 * 外键关联表: standard_specification - specification_number
	 **/
	@ApiModelProperty("外键 standard_specification - specification_number")
	Page<SelectOptionsVO> selectOptionsByProductInformation1(String productInformation1);
	/**
	 * 外键关联表: sys_group - group_code
	 **/
	@ApiModelProperty("外键 sys_group - group_code")
	Page<SelectOptionsVO> selectOptionsByTestingTeam(String testingTeam);
	/**
	 * 外键关联表: producet_management - product_model
	 **/
	@ApiModelProperty("外键 producet_management - product_model")
	Page<SelectOptionsVO> selectOptionsByProductModel(String productModel);


    /**
     * 根据条件查询试验项目列表
     *
     * @param dto 试验项目信息
     * @return 试验项目集合信息
     */
    List<ExperimentProjectVO> selectExperimentProjectList(ExperimentProjectDTO dto);

	/**
	 * 根据IDS查询试验项目列表
	 * @param ids
	 * @return
	 */
    List<ExperimentProjectVO> selectExperimentProjectListByIds(@Param("ids") List<String> ids);



}