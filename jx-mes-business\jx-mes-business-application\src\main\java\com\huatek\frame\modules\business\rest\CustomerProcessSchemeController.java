package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.utils.Constant;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.modules.business.domain.CustomerProcessScheme;
import com.huatek.frame.modules.business.service.CustomerProcessSchemeService;
import com.huatek.frame.modules.business.service.dto.CustomerProcessSchemeDTO;
import com.huatek.frame.modules.business.domain.vo.CustomerProcessSchemeVO;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-07-17
**/
@Api(tags = "客户工序方案管理")
@RestController
@RequestMapping("/api/customerProcessScheme")
public class CustomerProcessSchemeController {

	@Autowired
    private CustomerProcessSchemeService customerProcessSchemeService;

	/**
	 * 客户工序方案列表
	 * 
	 * @param dto 客户工序方案DTO 实体对象
	 * @return
	 */
    @Log("客户工序方案列表")
    @ApiOperation(value = "客户工序方案列表查询")
    @PostMapping(value = "/customerProcessSchemeList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("customerProcessScheme:list")
    public TorchResponse<List<CustomerProcessSchemeVO>> query(@RequestBody CustomerProcessSchemeDTO dto){
        return customerProcessSchemeService.findCustomerProcessSchemePage(dto);
    }

	/**
	 * 新增/修改客户工序方案
	 * 
	 * @param customerProcessSchemeDto 客户工序方案DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改客户工序方案")
    @ApiOperation(value = "客户工序方案新增/修改操作")
    @PostMapping(value = "/customerProcessScheme", produces = { "application/json;charset=utf-8" })
    @TorchPerm("customerProcessScheme:add#customerProcessScheme:edit")
    public TorchResponse add(@RequestBody CustomerProcessSchemeDTO customerProcessSchemeDto) throws Exception {
		// BeanValidatorFactory.validate(customerProcessSchemeDto);
		return customerProcessSchemeService.saveOrUpdate(customerProcessSchemeDto);
	}

	/**
	 * 查询客户工序方案详情
	 * 
	 * @param id 主键id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("客户工序方案详情")
    @ApiOperation(value = "客户工序方案详情查询")
    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("customerProcessScheme:detail")
	public TorchResponse detail(@PathVariable(value = "id") String id) {
		return customerProcessSchemeService.findCustomerProcessScheme(id);
	}



	/**
	 * 删除客户工序方案
	 * 
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
    @Log("删除客户工序方案")
    @ApiOperation(value = "客户工序方案删除操作")
    @TorchPerm("customerProcessScheme:del")
    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	public TorchResponse delete(@RequestBody String[] ids) {
		return customerProcessSchemeService.delete(ids);
	}

    @ApiOperation(value = "客户工序方案联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return customerProcessSchemeService.getOptionsList(id);
	}

    /**
     * 删除客户工序方案
     *
     * @param customerProcessSchemeDto
     * @return
     */
    @SuppressWarnings("rawtypes")
    @Log("设为标准工序方案")
    @ApiOperation(value = "设为标准工序方案")
    @TorchPerm("customerProcessScheme:setStandard")
    @PostMapping(value = "/setStandardProcessPlan", produces = { "application/json;charset=utf-8" })
    public TorchResponse setStandardProcessPlan(@RequestBody CustomerProcessSchemeDTO customerProcessSchemeDto) {
        return customerProcessSchemeService.setStandardProcessPlan(customerProcessSchemeDto);
    }






    @Log("客户工序方案导出")
    @ApiOperation(value = "客户工序方案导出")
    @TorchPerm("customerProcessScheme:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody CustomerProcessSchemeDTO dto)
    {
        List<CustomerProcessSchemeVO> list = customerProcessSchemeService.selectCustomerProcessSchemeList(dto);
        ExcelUtil<CustomerProcessSchemeVO> util = new ExcelUtil<CustomerProcessSchemeVO>(CustomerProcessSchemeVO.class);
        util.exportExcel(response, list, "客户工序方案数据");
    }

    @Log("根据Ids获取客户工序方案列表")
    @ApiOperation(value = "客户工序方案 根据Ids批量查询")
    @PostMapping(value = "/customerProcessSchemeList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getCustomerProcessSchemeListByIds(@RequestBody List<String> ids) {
        return customerProcessSchemeService.selectCustomerProcessSchemeListByIds(ids);
    }


}