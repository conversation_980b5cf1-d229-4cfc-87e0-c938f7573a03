package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.business.service.EvaluationOrderNotifyService;
import com.huatek.frame.modules.business.service.dto.EvaluationOrderNotifyDTO;
import com.huatek.frame.modules.business.service.dto.EvaluationOrderReviewPageDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "测评订单通知消息管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/evaluationOrderNotify")
public class EvaluationOrderNotifyController {

    private final EvaluationOrderNotifyService evaluationOrderNotifyService;
    /**
     * 查询测评订单通知消息列表
     * @param requestParam 测评订单通知消息请求信息
     * @return
     */
    @Log("查询测评订单通知消息列表")
    @ApiOperation(value = "查询测评订单通知消息列表")
    @PostMapping(value = "/findAll", produces = { "application/json;charset=utf-8" })
    public TorchResponse findAll(@RequestBody EvaluationOrderNotifyDTO requestParam){
        return evaluationOrderNotifyService.findAll(requestParam);
    }
}
