package com.huatek.frame.modules.business.rest;

import com.huatek.frame.modules.business.service.dto.EvaluationOrderNotifyDTO;
import com.huatek.frame.modules.business.service.dto.ProductBatchUpdateReqDTO;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.modules.business.service.ProductListService;
import com.huatek.frame.modules.business.service.dto.ProductListDTO;
import com.huatek.frame.modules.business.domain.vo.ProductListVO;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

/**
* <AUTHOR>
* @date 2025-07-30
**/
@Api(tags = "产品列表管理")
@RestController
@RequestMapping("/api/productList")
public class ProductListController {

	@Autowired
    private ProductListService productListService;

	/**
	 * 产品列表列表
	 * 
	 * @param dto 产品列表DTO 实体对象
	 * @return
	 */
    @Log("产品列表列表")
    @ApiOperation(value = "产品列表列表查询")
    @PostMapping(value = "/productListList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productList:list")
    public TorchResponse<List<ProductListVO>> query(@RequestBody ProductListDTO dto){
        return productListService.findProductListPage(dto);
    }

    /**
     * 根据测评订单id获取该订单下所有产品列表信息
     * @param evaluationOrderId 测评订单id
     * @return
     */
    @Log("根据测评订单id获取该订单下所有产品列表信息")
    @ApiOperation(value = "根据测评订单id获取该订单下所有产品列表信息")
    @GetMapping(value = "/findProductListsByEvaluationId", produces = { "application/json;charset=utf-8" })
    public TorchResponse<List<ProductListVO>> selectProductListsByEvaluationOrderId(@RequestParam String evaluationOrderId){
        return productListService.selectProductListsByEvaluationOrderId(evaluationOrderId);
    }

	/**
	 * 新增/修改产品列表
	 * 
	 * @param productListDto 产品列表DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改产品列表")
    @ApiOperation(value = "产品列表新增/修改操作")
    @PostMapping(value = "/add", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productList:add#productList:edit")
    public TorchResponse add(@RequestBody ProductListDTO productListDto) throws Exception {
		// BeanValidatorFactory.validate(productListDto);
		return productListService.saveOrUpdate(productListDto);
	}

	/**
	 * 查询产品列表详情
	 * 
	 * @param id 主键id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("产品列表详情")
    @ApiOperation(value = "产品列表详情查询")
    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productList:detail")
	public TorchResponse detail(@PathVariable(value = "id") String id) {
		return productListService.findProductList(id);
	}

	/**
	 * 删除产品列表
	 * 
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
    @Log("删除产品列表")
    @ApiOperation(value = "产品列表删除操作")
    @TorchPerm("productList:del")
    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	public TorchResponse delete(@RequestBody String[] ids) {
		return productListService.delete(ids);
	}

    @ApiOperation(value = "产品列表联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return productListService.getOptionsList(id);
	}





    @Log("产品列表导出")
    @ApiOperation(value = "产品列表导出")
    @TorchPerm("productList:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody ProductListDTO dto)
    {
        List<ProductListVO> list = productListService.selectProductDetails(dto);
        ExcelUtil<ProductListVO> util = new ExcelUtil<ProductListVO>(ProductListVO.class);
        util.exportExcel(response, list, "产品列表数据");
    }

    @Log("产品列表导入")
    @ApiOperation(value = "产品列表导入")
    @TorchPerm("productList:import")
    @PostMapping("/importData/{evaluationOrderId}")
    public TorchResponse importData(@RequestParam("file") MultipartFile file, @PathVariable(value = "evaluationOrderId") String evaluationOrderId) throws Exception
    {
        ExcelUtil<ProductListVO> util = new ExcelUtil<ProductListVO>(ProductListVO.class);
        List<ProductListVO> list = util.importExcel(file.getInputStream());
        return productListService.importProductList(list, evaluationOrderId);
    }

//    @Log("产品列表导入测试")
//    @ApiOperation(value = "产品列表导入")
//    @PostMapping("/importDataTest")
//    public TorchResponse importData(@RequestBody List<ProductListVO> requestParam) throws Exception
//    {
//
//        return productListService.importProductList(requestParam , "");
//    }

    @Log("产品列表导入模板")
    @ApiOperation(value = "产品列表导入模板下载")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<ProductListVO> util = new ExcelUtil<ProductListVO>(ProductListVO.class);
        util.importTemplateExcel(response, "产品列表数据");
    }

    @Log("根据Ids获取多个产品列表信息")
    @ApiOperation(value = "产品列表 根据Ids批量查询")
    @PostMapping(value = "/productListList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getproductListListByIds(@RequestBody List<String> ids) {
        return productListService.selectProductListListByIds(ids);
    }

    @Log("批量修改产品列表")
    @ApiOperation(value = "批量修改产品列表")
    @PostMapping(value = "/batchUpdate", produces = {"application/json;charset=utf-8"})
    public TorchResponse batchUpdateProductList(@RequestBody ProductBatchUpdateReqDTO requestParam){
        return productListService.batchUpdateProductList(requestParam);
    }

    @Log("变更通知")
    @ApiOperation(value = "变更通知")
    @PostMapping(value = "/notify", produces = {"application/json;charset=utf-8"})
    public TorchResponse notifyProductListChangeMessage(@RequestBody EvaluationOrderNotifyDTO requestParam){
        return productListService.notifyProductListChangeMessage(requestParam);
    }


}