package com.huatek.frame.modules.business.rest;

import com.huatek.frame.modules.business.domain.vo.CapabilityAssetVO;
import com.huatek.frame.modules.business.domain.vo.ProductionTaskVO;
import com.huatek.frame.modules.business.domain.vo.TestDataDictionaryVO;
import com.huatek.frame.modules.business.domain.vo.UnqualifiedProcessVO;
import com.huatek.frame.modules.business.service.ProductionTaskService;
import com.huatek.frame.modules.business.service.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

/**
* <AUTHOR>
* @date 2025-08-11
**/
@Api(tags = "生产任务管理")
@RestController
@RequestMapping("/api/productionTask")
public class ProductionTaskController {

	@Autowired
    private ProductionTaskService productionTaskService;

	/**
	 * 生产任务列表
	 * 
	 * @param dto 生产任务DTO 实体对象
	 * @return
	 */
    @Log("生产任务列表")
    @ApiOperation(value = "生产任务列表查询")
    @PostMapping(value = "/productionTaskList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionTask:list")
    public TorchResponse<List<ProductionTaskVO>> query(@RequestBody ProductionTaskDTO dto){
        dto.setIsAdmin(false);
        return productionTaskService.findProductionTaskPage(dto);
    }

	/**
	 * 新增/修改生产任务
	 * 
	 * @param productionTaskDto 生产任务DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改生产任务")
    @ApiOperation(value = "生产任务新增/修改操作")
    @PostMapping(value = "/add", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionTask:add#productionTask:edit")
    public TorchResponse add(@RequestBody ProductionTaskDTO productionTaskDto) throws Exception {
		// BeanValidatorFactory.validate(productionTaskDto);
		return productionTaskService.saveOrUpdate(productionTaskDto);
	}


	/**
	 * 查询生产任务详情
	 * 
	 * @param id 主键id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("生产任务详情")
    @ApiOperation(value = "生产任务详情查询")
    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionTask:detail")
	public TorchResponse detail(@PathVariable(value = "id") String id) {
		return productionTaskService.findProductionTask(id);
	}

	/**
	 * 删除生产任务
	 * 
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
    @Log("删除生产任务")
    @ApiOperation(value = "生产任务删除操作")
    @TorchPerm("productionTask:del")
    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	public TorchResponse delete(@RequestBody String[] ids) {
		return productionTaskService.delete(ids);
	}

    @ApiOperation(value = "生产任务联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return productionTaskService.getOptionsList(id);
	}




    @ApiOperation(value = "生产任务 自动填充数据查询")
    @GetMapping(value = "/linkageData/{linkageDataTableName}/{conditionalValue}", produces = { "application/json;charset=utf-8" })
    public TorchResponse getLinkageData(@PathVariable(value = "linkageDataTableName") String linkageDataTableName,
    									@PathVariable(value = "conditionalValue") String conditionalValue) {
		return productionTaskService.getLinkageData(linkageDataTableName, conditionalValue);
	}

    @Log("生产任务导出")
    @ApiOperation(value = "生产任务导出")
    @TorchPerm("productionTask:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody ProductionTaskDTO dto)
    {
        List<ProductionTaskVO> list = productionTaskService.selectProductionTaskList(dto);
        ExcelUtil<ProductionTaskVO> util = new ExcelUtil<ProductionTaskVO>(ProductionTaskVO.class);
        util.exportExcel(response, list, "生产任务数据");
    }

    @Log("生产任务导入")
    @ApiOperation(value = "生产任务导入")
    @TorchPerm("productionTask:import")
    @PostMapping("/importData")
    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
    {
        ExcelUtil<ProductionTaskVO> util = new ExcelUtil<ProductionTaskVO>(ProductionTaskVO.class);
        List<ProductionTaskVO> list = util.importExcel(file.getInputStream());
        return productionTaskService.importProductionTask(list, unionColumns, true, "");
    }

    @Log("生产任务导入模板")
    @ApiOperation(value = "生产任务导入模板下载")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<ProductionTaskVO> util = new ExcelUtil<ProductionTaskVO>(ProductionTaskVO.class);
        util.importTemplateExcel(response, "生产任务数据");
    }

    @Log("根据Ids获取生产任务列表")
    @ApiOperation(value = "生产任务 根据Ids批量查询")
    @PostMapping(value = "/productionTaskList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getProductionTaskListByIds(@RequestBody List<String> ids) {
        return productionTaskService.selectProductionTaskListByIds(ids);
    }

    @Log("新增异常反馈")
    @ApiOperation(value = "异常反馈新增操作")
    @PostMapping(value = "/abnormalfeedback", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionTask:abnormalfeedback")
    public TorchResponse abnormalfeedback(@RequestBody AbnormalfeedbackDTO abnormalfeedbackDto) throws Exception {
        return productionTaskService.abnormalfeedback(abnormalfeedbackDto);
    }
    @Log("新增外协")
    @ApiOperation(value = "外协新增操作")
    @PostMapping(value = "/outsourcing", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionTask:outsourcing")
    public TorchResponse outsourcing(@RequestBody AddOrUpdateOutsourcingsDTO addOrUpdateOutsourcingsDTO) throws Exception {
        return productionTaskService.outsourcing(addOrUpdateOutsourcingsDTO);
    }
    /**
     * 扫码接口
     *
     * @param scanCodeDto 扫码请求DTO
     * @return 扫码响应结果
     */
    @Log("扫码接口")
    @ApiOperation(value = "扫码接口")
    @PostMapping(value = "/scanCode", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionTask:scanCode")
    public TorchResponse scanCode(@RequestBody ScanCodeDTO scanCodeDto) throws Exception {
        return productionTaskService.scanCode(scanCodeDto);
    }

    /**
     * 开始任务接口
     *
     * @param taskIds
     * @return 开始任务响应结果
     */
    @Log("开始任务接口")
    @ApiOperation(value = "开始任务接口")
    @PostMapping(value = "/startTask", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionTask:startTask")
    public TorchResponse startTask(@RequestBody List<String> taskIds) throws Exception {
        return productionTaskService.startTask(taskIds);
    }


    /**
     * 暂停任务
     *
     * @param taskOperationDto 任务操作DTO
     * @return 操作结果
     */
    @Log("暂停任务")
    @ApiOperation(value = "暂停任务")
    @PostMapping(value = "/pauseTask", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionTask:pause")
    public TorchResponse pauseTask(@RequestBody TaskOperationDTO taskOperationDto) throws Exception {
        return productionTaskService.pauseTask(taskOperationDto);
    }

    /**
     * 恢复任务
     *
     * @param id 任务操作DTO
     * @return 操作结果
     */
    @Log("恢复任务")
    @ApiOperation(value = "恢复任务")
    @PostMapping(value = "/resumeTask/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionTask:resume")
    public TorchResponse resumeTask(@PathVariable String id) throws Exception {
        return productionTaskService.resumeTask(id);
    }

    /**
     * 取消任务
     *
     * @param taskOperationDto 任务操作DTO
     * @return 操作结果
     */
    @Log("取消任务")
    @ApiOperation(value = "取消任务")
    @PostMapping(value = "/cancelTask", produces = { "application/json;charset=utf-8" })
    public TorchResponse cancelTask(@RequestBody TaskOperationDTO taskOperationDto) throws Exception {
        return productionTaskService.cancelTask(taskOperationDto);
    }

    /**
     * 消除PDA预警
     *
     * @param ids 生产任务ID列表
     * @return 操作结果
     */
    @Log("消除PDA预警")
    @ApiOperation(value = "消除PDA预警")
    @PostMapping(value = "/clearPdaWarning", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionTask:clearPdaWarning")
    public TorchResponse clearPdaWarning(@RequestBody List<String> ids) throws Exception {
        return productionTaskService.clearPdaWarning(ids);
    }

    /**
     * 提交任务
     *
     * @param submitTaskDto 提交任务DTO
     * @return
     */
    @Log("提交任务")
    @ApiOperation(value = "提交任务")
    @PostMapping(value = "/submitTask", produces = {"application/json;charset=utf-8"})
    @TorchPerm("productionTask:submitTask")
    public TorchResponse submitTask(@RequestBody SubmitTaskDTO submitTaskDto) {
        return productionTaskService.submitTask(submitTaskDto);
    }

    /**
     * 审批通过任务（支持单个或批量）
     *
     * @param approveTaskDto 审批通过任务DTO
     * @return
     */
    @Log("审批通过任务")
    @ApiOperation(value = "审批通过任务（支持单个或批量）")
    @PostMapping(value = "/approveTask", produces = {"application/json;charset=utf-8"})
    @TorchPerm("productionTask:approveTask")
    public TorchResponse approveTask(@RequestBody ApproveTaskDTO approveTaskDto) {
        return productionTaskService.approveTask(approveTaskDto);
    }

    /**
     * 审批驳回任务（支持单个或批量）
     *
     * @param rejectTaskDto 审批驳回任务DTO
     * @return
     */
    @Log("审批驳回任务")
    @ApiOperation(value = "审批驳回任务（支持单个或批量）")
    @PostMapping(value = "/rejectTask", produces = {"application/json;charset=utf-8"})
    @TorchPerm("productionTask:rejectTask")
    public TorchResponse rejectTask(@RequestBody RejectTaskDTO rejectTaskDto) {
        return productionTaskService.rejectTask(rejectTaskDto);
    }


//=======================下边的方法测试完 注释掉================================================
    /**
     * 批量新增生产任务
     *
     * @param productionTaskDtoList 生产任务DTO实体对象
     * @return
     * @throws Exception
     */
    @SuppressWarnings("rawtypes")
    @Log("批量新增生产任务")
    @ApiOperation(value = "生产任务批量新增操作")
    @PostMapping(value = "/batchAdd", produces = { "application/json;charset=utf-8" })
    public TorchResponse batchAdd(@RequestBody List<ProductionTaskDTO> productionTaskDtoList) throws Exception {
        // BeanValidatorFactory.validate(productionTaskDto);
        return productionTaskService.saveBatch(productionTaskDtoList);
    }

    /**
     * 分单
     *
     * @param splitOrderProductionTaskDTOList 生产任务DTO实体对象
     * @return
     * @throws Exception
     */
    @SuppressWarnings("rawtypes")
    @Log("分单")
    @ApiOperation(value = "分单")
    @PostMapping(value = "/splitOrder", produces = { "application/json;charset=utf-8" })
    public TorchResponse splitOrder(@RequestBody List<SplitOrderProductionTaskDTO> splitOrderProductionTaskDTOList) throws Exception {
        // BeanValidatorFactory.validate(productionTaskDto);
        return productionTaskService.splitOrder(splitOrderProductionTaskDTOList);
    }

    @Log("外协审批通过")
    @ApiOperation(value = "外协审批通过")
    @PostMapping(value = "/outsourcingPass", produces = { "application/json;charset=utf-8" })
    public TorchResponse outsourcingPass(@RequestBody String taskId) throws Exception {
        return productionTaskService.outsourcingPass(taskId);
    }
    @Log("外协审批验收")
    @ApiOperation(value = "外协审批验收")
    @PostMapping(value = "/outsourcingAccept", produces = { "application/json;charset=utf-8" })
    public TorchResponse outsourcingAccept(@RequestBody String taskId) throws Exception {
        return productionTaskService.outsourcingAccept(taskId);
    }

    /**
     * 通过工单编号查询最后一个工序的合格数量
     *
     * @param workOrderNumber 工单编号
     * @return 最后一个工序的合格数量
     */
    @Log("查询最后一个工序的合格数量")
    @ApiOperation(value = "通过工单编号查询最后一个工序的合格数量")
    @GetMapping(value = "/lastProcessQualifiedQuantity/{workOrderNumber}", produces = { "application/json;charset=utf-8" })
    public TorchResponse<Long> getLastProcessQualifiedQuantity(@PathVariable String workOrderNumber) throws Exception {
        return productionTaskService.getLastProcessQualifiedQuantity(workOrderNumber);
    }

    /**
     * 通过工单编号查询所有的不合格工序和不合格原因
     *
     * @param workOrderNumber 工单编号
     * @return 工单质量信息
     */
    @Log("查询工单质量信息")
    @ApiOperation(value = "通过工单编号查询所有的不合格工序和不合格原因")
    @GetMapping(value = "/unqualified/{workOrderNumber}", produces = { "application/json;charset=utf-8" })
    public TorchResponse<List<UnqualifiedProcessVO>> getUnqualifiedTaskInfo(@PathVariable String workOrderNumber) throws Exception {
        return productionTaskService.getUnqualifiedTaskInfo(workOrderNumber);
    }
    /**
     * 查询技术能力
     * 参数为productionTaskId
     * 1.通过工序编号standardProcessManagement查询工序分类process_classification
     * 2.通过产品型号（product_model）+process_classification（capability_type）去capability_asset查数据
     */
    @Log("查询技术能力")
    @ApiOperation(value = "查询技术能力")
    @GetMapping(value = "/capability/{productionTaskId}", produces = { "application/json;charset=utf-8" })
    public TorchResponse<List<CapabilityAssetVO>> getCapability(@PathVariable String productionTaskId) throws Exception {
        return productionTaskService.getCapability(productionTaskId);
    }

    @Log("查询试验数据字典")
    @ApiOperation(value = "查询试验数据字典testType experimentProject fieldName")
    @GetMapping(value = "/testDataDictionary", produces = { "application/json;charset=utf-8" })
    public TorchResponse<List<String>> getTestDataDictionary(TestDataDictionaryDTO testDataDictionaryDTO) throws Exception {
        return productionTaskService.getTestDataDictionary(testDataDictionaryDTO);
    }
    
}