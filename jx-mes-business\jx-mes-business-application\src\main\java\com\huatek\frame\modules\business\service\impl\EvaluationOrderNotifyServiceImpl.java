package com.huatek.frame.modules.business.service.impl;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.modules.business.domain.EvaluationOrderNotify;
import com.huatek.frame.modules.business.domain.vo.EvaluationOrderNotifyVO;
import com.huatek.frame.modules.business.mapper.EvaluationOrderNotifyMapper;
import com.huatek.frame.modules.business.service.EvaluationOrderNotifyService;
import com.huatek.frame.modules.business.service.dto.EvaluationOrderNotifyDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 测评订单消息通知服务实现层
 */
@Service
@RequiredArgsConstructor
public class EvaluationOrderNotifyServiceImpl extends ServiceImpl<EvaluationOrderNotifyMapper, EvaluationOrderNotify> implements EvaluationOrderNotifyService {

    private final EvaluationOrderNotifyMapper evaluationOrderNotifyMapper;

    @Override
    public TorchResponse<List<EvaluationOrderNotifyVO>> findAll(EvaluationOrderNotifyDTO requestParam) {
        PageHelper.startPage(requestParam.getPage(), requestParam.getLimit());
        Page<EvaluationOrderNotifyVO> evaluationOrderNotifyVOS = evaluationOrderNotifyMapper.findAll(requestParam);
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setCount(evaluationOrderNotifyVOS.getTotal());
        return response;
    }
}
