package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;

import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;

import com.huatek.frame.modules.business.service.CodeManagementService;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.PackageOrder;
import com.huatek.frame.modules.business.domain.vo.PackageOrderVO;
import com.huatek.frame.modules.business.mapper.PackageOrderMapper;
import com.huatek.frame.modules.business.service.PackageOrderService;
import com.huatek.frame.modules.business.service.dto.PackageOrderDTO;
import java.sql.Date;
import com.huatek.frame.modules.business.domain.CustomerInformationManagement;
import com.huatek.frame.modules.business.mapper.CustomerInformationManagementMapper;
import org.springframework.util.CollectionUtils;

import com.huatek.frame.modules.business.domain.vo.ProductInformationVO;
import com.huatek.frame.modules.business.service.ProductInformationService;
import com.huatek.frame.modules.business.service.dto.ProductInformationDTO;




/**
 * 封装订单 ServiceImpl
 * <AUTHOR>
 * @date 2025-07-22
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "packageOrder")
//@RefreshScope
@Slf4j
public class PackageOrderServiceImpl implements PackageOrderService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private PackageOrderMapper packageOrderMapper;

	@Autowired
    private CustomerInformationManagementMapper customerInformationManagementMapper;

    @Autowired
    protected Validator validator;

    @Autowired
    private CodeManagementService codeManagementService;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();


    @Autowired
    private ProductInformationService productInformationService;


	public PackageOrderServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<PackageOrderVO>> findPackageOrderPage(PackageOrderDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<PackageOrderVO> packageOrders = packageOrderMapper.selectPackageOrderPage(dto);
		TorchResponse<List<PackageOrderVO>> response = new TorchResponse<List<PackageOrderVO>>();
		response.getData().setData(packageOrders);
		response.setStatus(200);
		response.getData().setCount(packageOrders.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(PackageOrderDTO packageOrderDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(packageOrderDto.getCodexTorchDeleted())) {
            packageOrderDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = packageOrderDto.getId();
		PackageOrder entity = new PackageOrder();
        BeanUtils.copyProperties(packageOrderDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
            TorchResponse response = codeManagementService.getOrderNumber("FZDD");
            entity.setOrderNumber(response.getData().getData().toString());
            entity.setOrderStatus(DicConstant.SalesOrder.PACKAGE_ORDER_STATUS_IN_PROGRESS);
            packageOrderMapper.insert(entity);
		} else {
			packageOrderMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        PackageOrderVO vo = new PackageOrderVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<PackageOrderVO> findPackageOrder(String id) {
		PackageOrderVO vo = new PackageOrderVO();
		if (!HuatekTools.isEmpty(id)) {
			PackageOrder entity = packageOrderMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<PackageOrderVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
		packageOrderMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("customerId0",packageOrderMapper::selectOptionsByCustomerId0);
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}




    @Override
	public TorchResponse getLinkageData(String linkageDataTableName, String conditionalValue) {
	    Map<String, String> data = new HashMap();
        try {
	        switch (linkageDataTableName) {
                case "customer_information_management":
                    data = selectDataLinkageByCustomerId0(conditionalValue);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("查询数据异常，请联系管理员！");
        }
	    TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(data);
        return response;
	}
    @Override
    public Map<String,String> selectDataLinkageByCustomerId0(String customer_id0) {
        return packageOrderMapper.selectDataLinkageByCustomerId0(customer_id0);
    }

    @Override
    @ExcelExportConversion(tableName = "package_order", convertorFields = "capability_review_feedbackProcessing#capabilityFeedback,orderType,orderStatus,urgencyLevel")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<PackageOrderVO> selectPackageOrderList(PackageOrderDTO dto) {
        return packageOrderMapper.selectPackageOrderList(dto);
    }

    /**
     * 导入封装订单数据
     *
     * @param packageOrderList 封装订单数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "package_order", convertorFields = "orderType,orderStatus,urgencyLevel")
    public TorchResponse importPackageOrder(List<PackageOrderVO> packageOrderList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(packageOrderList) || packageOrderList.size() == 0) {
            throw new ServiceException("导入封装订单数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (PackageOrderVO vo : packageOrderList) {
            try {
                PackageOrder packageOrder = new PackageOrder();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, packageOrder);
                QueryWrapper<PackageOrder> wrapper = new QueryWrapper();
                PackageOrder oldPackageOrder = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = PackageOrderVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<PackageOrder> oldPackageOrderList = packageOrderMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldPackageOrderList) && oldPackageOrderList.size() > 1) {
                        packageOrderMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldPackageOrderList) && oldPackageOrderList.size() == 1) {
                        oldPackageOrder = oldPackageOrderList.get(0);
                    }
                }
                if (StringUtils.isNull(oldPackageOrder)) {
                    BeanValidators.validateWithException(validator, vo);
                    packageOrderMapper.insert(packageOrder);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、订单编号 " + vo.getOrderNumber() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldPackageOrder, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    packageOrderMapper.updateById(oldPackageOrder);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、订单编号 " + vo.getOrderNumber() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、订单编号 " + vo.getOrderNumber() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、订单编号 " + vo.getOrderNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(PackageOrderVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (HuatekTools.isEmpty(vo.getOrderNumber())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>订单编号不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getOrderType())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>订单类型不能为空!");
        }
        if (!HuatekTools.isEmpty(vo.getCustomerId0())) {
            List<String> customerId0List = Arrays.asList(vo.getCustomerId0().split(","));
            List<CustomerInformationManagement> list = customerInformationManagementMapper.selectList(new QueryWrapper<CustomerInformationManagement>().in("customer_id0", customerId0List));
            if (CollectionUtils.isEmpty(list)) {
                failureRecord++;
                failureRecordMsg.append("客户编号=" + vo.getCustomerId0() + "; ");
            }
        }
        if (HuatekTools.isEmpty(vo.getSettlementUnit())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>结算单位不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getRecipient())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>接收人不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getDateOfEntrustment())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>委托日期不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getUrgencyLevel())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>紧急程度不能为空!");
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectPackageOrderListByIds(List<String> ids) {
        List<PackageOrderVO> packageOrderList = packageOrderMapper.selectPackageOrderListByIds(ids);

		TorchResponse<List<PackageOrderVO>> response = new TorchResponse<List<PackageOrderVO>>();
		response.getData().setData(packageOrderList);
		response.setStatus(200);
		response.getData().setCount((long)packageOrderList.size());
		return response;
    }

    /**
     * 封装订单主子表单组合提交
     *
	 * @param packageOrderDto 封装订单DTO实体对象
     * @return
     */
    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse submitMasterDetails(PackageOrderDTO packageOrderDto){
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(packageOrderDto.getCodexTorchDeleted())) {
            packageOrderDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }

        //非必要字段处理
        packageOrderDto.setId("");
        packageOrderDto.setCodexTorchDeleted(Constant.DEFAULT_NO);

        //TODO: TorchDetailItemIds TO BE DEPRECATED
        packageOrderDto.setCodexTorchDetailItemIds("");

        TorchResponse<PackageOrderVO> masterSubmitResp = this.saveOrUpdate(packageOrderDto);
        PackageOrderVO masterVo = masterSubmitResp.getData().getData();

        List<ProductInformationDTO> productInformationDTOs = new ArrayList<>();
        if (packageOrderDto.getDetailFormItems() != null && packageOrderDto.getDetailFormItems().length > 0) {
            productInformationDTOs = Arrays.asList(packageOrderDto.getDetailFormItems());
        } else if (StringUtils.isNotEmpty(packageOrderDto.getCodexTorchDetailItemIds())) {
        } else {
            throw new ServiceException("表单提交异常，表单明细项为空");
        }

        for(ProductInformationDTO productInformationDto : productInformationDTOs){
            productInformationDto.setId("");

            //非必要字段处理
            productInformationDto.setCodexTorchDeleted(Constant.DEFAULT_NO);

            //主子表关联ID
            productInformationDto.setCodexTorchMasterFormId(masterVo.getId());
            // 业务字段管理
            productInformationDto.setOrderNumber(masterVo.getOrderNumber());
            //提交
            TorchResponse<ProductInformationVO> detailSubmitResp = productInformationService.saveOrUpdate(productInformationDto);
            ProductInformationVO detailVo = detailSubmitResp.getData().getData();
        }

		TorchResponse response = new TorchResponse();
        response.getData().setData(masterVo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
    }


}
