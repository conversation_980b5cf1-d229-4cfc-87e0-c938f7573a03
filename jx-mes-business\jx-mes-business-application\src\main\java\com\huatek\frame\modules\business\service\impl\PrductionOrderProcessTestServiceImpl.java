package com.huatek.frame.modules.business.service.impl;

import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.modules.business.domain.ProductionOrderProcessTest;
import com.huatek.frame.modules.business.domain.vo.ProductionOrderProcessTestVO;
import com.huatek.frame.modules.business.domain.vo.ProductionOrderResultVO;
import com.huatek.frame.modules.business.mapper.ProductionOrderProcessTestMapper;
import com.huatek.frame.modules.business.mapper.ProductionOrderResultMapper;
import com.huatek.frame.modules.business.service.ProductionOrderProcessTestService;
import com.huatek.frame.modules.business.service.ProductionOrderResultService;
import com.huatek.frame.modules.business.service.dto.ProductionOrderProcessTestDTO;
import com.huatek.frame.modules.business.service.dto.ProductionOrderResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Validator;
import java.util.List;


/**
 * 工单检验结果 ServiceImpl
 * <AUTHOR>
 * @date 2025-08-04
 */
@Service
@DubboService
@Slf4j
public class PrductionOrderProcessTestServiceImpl implements ProductionOrderProcessTestService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

    @Autowired
    protected Validator validator;


    @Autowired
    private ProductionOrderProcessTestMapper productionOrderProcessTestMapper;
	public PrductionOrderProcessTestServiceImpl(){

	}

    @Override
    public TorchResponse saveOrUpdate(ProductionOrderProcessTestDTO productionOrderProcessTestDTO) {
        ProductionOrderProcessTest test = new ProductionOrderProcessTest();
        BeanUtils.copyProperties(productionOrderProcessTestDTO,test);
        productionOrderProcessTestMapper.insert(test);
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }
}
