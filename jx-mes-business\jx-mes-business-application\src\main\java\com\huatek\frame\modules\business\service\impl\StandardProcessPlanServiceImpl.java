package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.domain.*;
import com.huatek.frame.modules.business.domain.vo.*;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.business.mapper.ExperimentProjectDataMapper;
import com.huatek.frame.modules.business.mapper.ExperimentProjectMapper;
import com.huatek.frame.modules.business.mapper.StandardProcessManagementMapper;
import com.huatek.frame.modules.business.service.ExperimentProjectDataService;
import com.huatek.frame.modules.business.service.dto.ExperimentProjectDataDTO;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.mapper.StandardProcessPlanMapper;
import com.huatek.frame.modules.business.service.StandardProcessPlanService;
import com.huatek.frame.modules.business.service.dto.StandardProcessPlanDTO;
import java.sql.Date;
import java.util.stream.Collectors;

import org.springframework.util.CollectionUtils;

import com.huatek.frame.modules.business.service.ExperimentProjectService;
import com.huatek.frame.modules.business.service.dto.ExperimentProjectDTO;




/**
 * 标准工序方案 ServiceImpl
 * <AUTHOR>
 * @date 2025-07-17
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "standardProcessPlan")
//@RefreshScope
@Slf4j
public class StandardProcessPlanServiceImpl implements StandardProcessPlanService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

    @Autowired
    private StandardProcessManagementMapper standardProcessManagementMapper;
	@Autowired
	private StandardProcessPlanMapper standardProcessPlanMapper;
    @Autowired
    private ExperimentProjectMapper experimentProjectMapper;
    @Autowired
    private ExperimentProjectDataMapper experimentProjectDataMapper;


    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();


    @Autowired
    private ExperimentProjectService experimentProjectService;
    @Autowired
    private ExperimentProjectDataService experimentProjectDataService;


	public StandardProcessPlanServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<StandardProcessPlanVO>> findStandardProcessPlanPage(StandardProcessPlanDTO dto) {
        if(!HuatekTools.isEmpty(dto.getTestPackage())) {
           String standardProcessPlan = dto.getTestPackage().replaceAll(",", "|");
           standardProcessPlan = MATCH_MULTIPLE_VALUE_REGEXP.replace("#", standardProcessPlan);
           dto.setTestPackage(standardProcessPlan);
        }
        if(!HuatekTools.isEmpty(dto.getStatus())) {
           String standardProcessPlan = dto.getStatus().replaceAll(",", "|");
           standardProcessPlan = MATCH_MULTIPLE_VALUE_REGEXP.replace("#", standardProcessPlan);
           dto.setStatus(standardProcessPlan);
        }
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<StandardProcessPlanVO> standardProcessPlans = standardProcessPlanMapper.selectStandardProcessPlanPage(dto);
		TorchResponse<List<StandardProcessPlanVO>> response = new TorchResponse<List<StandardProcessPlanVO>>();
		response.getData().setData(standardProcessPlans);
		response.setStatus(200);
		response.getData().setCount(standardProcessPlans.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(StandardProcessPlanDTO standardProcessPlanDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(standardProcessPlanDto.getCodexTorchDeleted())) {
            standardProcessPlanDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = standardProcessPlanDto.getId();
		StandardProcessPlan entity = new StandardProcessPlan();
        BeanUtils.copyProperties(standardProcessPlanDto, entity);
        entity.setDepartment(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
			standardProcessPlanMapper.insert(entity);
		} else {
			standardProcessPlanMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        StandardProcessPlanVO vo = new StandardProcessPlanVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<StandardProcessPlanVO> findStandardProcessPlan(String id) {
		StandardProcessPlanVO vo = new StandardProcessPlanVO();
		if (!HuatekTools.isEmpty(id)) {
			StandardProcessPlan entity = standardProcessPlanMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<StandardProcessPlanVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
		standardProcessPlanMapper.deleteBatchIds(Arrays.asList(ids));

		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
            selectOptionsFuncMap.put("entrustedUnit",standardProcessPlanMapper::selectOptionsByEntrustedUnit);
            selectOptionsFuncMap.put("standardSpecificationNumber",standardProcessPlanMapper::selectOptionsByStandardSpecificationNumber);
            selectOptionsFuncMap.put("productModel",standardProcessPlanMapper::selectOptionsByProductModel);
            selectOptionsFuncMap.put("productCategory",standardProcessPlanMapper::selectOptionsByProductCategory);
            selectOptionsFuncMap.put("department",standardProcessPlanMapper::selectOptionsByDepartment);
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "standard_process_plan", convertorFields = "testPackage,status")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<StandardProcessPlanVO> selectStandardProcessPlanList(StandardProcessPlanDTO dto) {
        return standardProcessPlanMapper.selectStandardProcessPlanList(dto);
    }

    /**
     * 导入标准工序方案数据
     *
     * @param standardProcessPlanList 标准工序方案数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "standard_process_plan", convertorFields = "processSchemeCategory,testPackage,status")
    public TorchResponse importStandardProcessPlan(List<StandardProcessPlanVO> standardProcessPlanList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(standardProcessPlanList) || standardProcessPlanList.size() == 0) {
            throw new ServiceException("导入标准工序方案数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (StandardProcessPlanVO vo : standardProcessPlanList) {
            try {
                StandardProcessPlan standardProcessPlan = new StandardProcessPlan();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, standardProcessPlan);
                QueryWrapper<StandardProcessPlan> wrapper = new QueryWrapper();
                StandardProcessPlan oldStandardProcessPlan = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = StandardProcessPlanVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<StandardProcessPlan> oldStandardProcessPlanList = standardProcessPlanMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldStandardProcessPlanList) && oldStandardProcessPlanList.size() > 1) {
                        standardProcessPlanMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldStandardProcessPlanList) && oldStandardProcessPlanList.size() == 1) {
                        oldStandardProcessPlan = oldStandardProcessPlanList.get(0);
                    }
                }
                if (StringUtils.isNull(oldStandardProcessPlan)) {
                    BeanValidators.validateWithException(validator, vo);
                    standardProcessPlanMapper.insert(standardProcessPlan);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、试验类型 " + vo.getTestType() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldStandardProcessPlan, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    standardProcessPlanMapper.updateById(oldStandardProcessPlan);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、试验类型 " + vo.getTestType() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、试验类型 " + vo.getTestType() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、试验类型 " + vo.getTestType() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(StandardProcessPlanVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (HuatekTools.isEmpty(vo.getTestType())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>试验类型不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getProcessSchemeName())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>工序方案名称不能为空!");
        }
//        if (HuatekTools.isEmpty(vo.getProcessSchemeCategory())) {
//            failureRecord++;
//            failureNotNullMsg.append("<br/>" + failureRecord + "=>工序方案分类不能为空!");
//        }
        if (HuatekTools.isEmpty(vo.getStandardSpecificationNumber())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>标准规范号不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getManufacturer())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>生产厂家不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getStatus())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>状态不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getCodexTorchUpdater())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>更新人不能为空!");
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectStandardProcessPlanListByIds(List<String> ids) {
        List<StandardProcessPlanVO> standardProcessPlanList = standardProcessPlanMapper.selectStandardProcessPlanListByIds(ids);

		TorchResponse<List<StandardProcessPlanVO>> response = new TorchResponse<List<StandardProcessPlanVO>>();
		response.getData().setData(standardProcessPlanList);
		response.setStatus(200);
		response.getData().setCount((long)standardProcessPlanList.size());
		return response;
    }

    /**
     * 标准工序方案主子表单组合提交
     *
	 * @param standardProcessPlanDto 标准工序方案DTO实体对象
     * @return
     */
    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse submitMasterDetails(StandardProcessPlanDTO standardProcessPlanDto){
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(standardProcessPlanDto.getCodexTorchDeleted())) {
            standardProcessPlanDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }

        //非必要字段处理
        standardProcessPlanDto.setId("");
        standardProcessPlanDto.setCodexTorchDeleted(Constant.DEFAULT_NO);

        //TODO: TorchDetailItemIds TO BE DEPRECATED
        standardProcessPlanDto.setCodexTorchDetailItemIds("");

        TorchResponse<StandardProcessPlanVO> masterSubmitResp = this.saveOrUpdate(standardProcessPlanDto);
        StandardProcessPlanVO masterVo = masterSubmitResp.getData().getData();

        List<ExperimentProjectDTO> experimentProjectDTOs = new ArrayList<>();
        List<ExperimentProjectDataDTO> experimentProjectDataDTOs = new ArrayList<>();
        if (standardProcessPlanDto.getDetailFormItems() != null && standardProcessPlanDto.getDetailFormItems().length > 0) {
            experimentProjectDTOs = Arrays.asList(standardProcessPlanDto.getDetailFormItems());
        } else if (StringUtils.isNotEmpty(standardProcessPlanDto.getCodexTorchDetailItemIds())) {
        } else {
            throw new ServiceException("表单提交异常，表单明细项为空");
        }

        for(ExperimentProjectDTO experimentProjectDto : experimentProjectDTOs){
            experimentProjectDataDTOs = Arrays.asList(experimentProjectDto.getProjectDataItems());
            experimentProjectDto.setId("");

            //非必要字段处理
            experimentProjectDto.setCodexTorchDeleted(Constant.DEFAULT_NO);

            //主子表关联ID
            experimentProjectDto.setCodexTorchMasterFormId(masterVo.getId());
            // 业务字段管理
            experimentProjectDto.setProcessSchemeName(masterVo.getProcessSchemeName());
            StandardProcessManagement  standardProcessManagement =standardProcessManagementMapper.selectById(experimentProjectDto.getProcessId());
            experimentProjectDto.setProcessCode3(standardProcessManagement.getStepNumber());
            //提交
            TorchResponse<ExperimentProjectVO> detailSubmitResp = experimentProjectService.saveOrUpdate(experimentProjectDto);
            ExperimentProjectVO detailVo = detailSubmitResp.getData().getData();
            for (ExperimentProjectDataDTO experimentProjectDataDto : experimentProjectDataDTOs){
                experimentProjectDataDto.setId("");

                //非必要字段处理
                experimentProjectDataDto.setCodexTorchDeleted(Constant.DEFAULT_NO);

                //主子表关联ID
                experimentProjectDataDto.setCodexTorchMasterFormId(detailVo.getId());
                // 业务字段管理
                experimentProjectDataDto.setStandardProcessCode(detailVo.getProcessCode3());
                //提交
                TorchResponse<ExperimentProjectDataVO> detaiDatalSubmitResp = experimentProjectDataService.saveOrUpdate(experimentProjectDataDto);
                ExperimentProjectDataVO detailDataVo = detaiDatalSubmitResp.getData().getData();

            }
        }

		TorchResponse response = new TorchResponse();
        response.getData().setData(masterVo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
    }

    @Override
    public TorchResponse getLinkageData(String linkageDataTableName, String conditionalValue) {
        Map<String, String> data = new HashMap();
        try {
            switch (linkageDataTableName) {
                case "customer_information_management":
                    data = selectDataLinkageByEntrustedUnit(conditionalValue);
                    break;
                case "product_management":
                    data = selectDataLinkageByProductModel(conditionalValue);
                    break;
                case "standard_specification":
                    data = selectDataLinkageByStandardSpecificationNumber(conditionalValue);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("查询数据异常，请联系管理员！");
        }
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(data);
        return response;
    }
    @Override
    public Map<String,String> selectDataLinkageByEntrustedUnit(String entrustedUnit) {
        return standardProcessPlanMapper.selectDataLinkageByEntrustedUnit(entrustedUnit);
    }
    @Override
    public Map<String,String> selectDataLinkageByProductModel(String productModel) {
        return standardProcessPlanMapper.selectDataLinkageByProductModel(productModel);
    }
    @Override
    public Map<String,String> selectDataLinkageByStandardSpecificationNumber(String specification_number) {
        return standardProcessPlanMapper.selectDataLinkageByStandardSpecificationNumber(specification_number);
    }

    @Override
    public TorchResponse copyStandProcessPlan(StandardProcessPlanDTO standardProcessPlanDto) {

        StandardProcessPlan plan = standardProcessPlanMapper.selectById(standardProcessPlanDto.getId());
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("CODEX_TORCH_MASTER_FORM_ID", standardProcessPlanDto.getId());
        List<ExperimentProject> experimentProjects = experimentProjectMapper.selectList(wrapper);
        List<String> experimentProjectids = experimentProjects.stream().map(ExperimentProject::getId)
                .collect(Collectors.toList());
        wrapper.clear();
        wrapper.in("CODEX_TORCH_MASTER_FORM_ID", experimentProjectids);
        List<ExperimentProjectData> experimentProjectDatas = experimentProjectDataMapper.selectList(wrapper);

        StandardProcessPlan entity = new StandardProcessPlan();
        BeanUtils.copyProperties(plan, entity);
        entity.setProcessSchemeName(standardProcessPlanDto.getProcessSchemeName());
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
        entity.setId(null);
        standardProcessPlanMapper.insert(entity);
        for (ExperimentProject experimentProject : experimentProjects) {
            ExperimentProject experimentEntity = new ExperimentProject();
            BeanUtils.copyProperties(experimentProject, experimentEntity);
            experimentEntity.setId(null);
            experimentEntity.setProcessSchemeName(standardProcessPlanDto.getProcessSchemeName());
            experimentEntity.setCodexTorchMasterFormId(entity.getId());
            experimentEntity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
            experimentEntity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
            experimentEntity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
            experimentProjectMapper.insert(experimentEntity);
            for (ExperimentProjectData projectdata : experimentProjectDatas) {
                ExperimentProjectData experimentDataEntity = new ExperimentProjectData();
                BeanUtils.copyProperties(projectdata, experimentDataEntity);
                experimentDataEntity.setId(null);
                experimentDataEntity.setCodexTorchMasterFormId(experimentEntity.getId());
                experimentDataEntity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
                experimentDataEntity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
                experimentProjectDataMapper.insert(experimentDataEntity);
            }
        }
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }
}
