<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.AwaitingProductionOrderMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.work_order_number as workOrderNumber,
		t.order_number as orderNumber,
		t.test_methodology as testMethodology,
		t.predecessor_work_order as predecessorWorkOrder,
		t.related_work_order as relatedWorkOrder,
		t.pda as pda,
		t.whether_to_include_in_scheduling as whetherToIncludeInScheduling,
		t.estimated_completion_time as estimatedCompletionTime,
		t.quantity as quantity,
		t.attachment as attachment,
		t.work_order_status as workOrderStatus,
<!--		t.responsible_person as responsible<PERSON>erson,-->
		t.production_stage as productionStage,
		t.wtstabr as wtstabr,
		t.whether_to_enter_components as whetherToEnterComponents,
		t.whether_to_enter_documents as whetherToEnterDocuments,
		t.completion_time as completionTime,
		t.wmfcnr as wmfcnr,
		t.irretrievable_reason as irretrievableReason,
        t.package_form as packageForm,
        t.non_aging_reason as nonAgingReason,
        t.codex_torch_applicant as codexTorchApplicant,
        t.codex_torch_approver as codexTorchApprover,
        t.codex_torch_approvers as codexTorchApprovers,
        t.codex_torch_approval_status as codexTorchApprovalStatus,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>
	<select id="selectAwaitingProductionOrderPage" parameterType="com.huatek.frame.modules.business.service.dto.ProductionOrderDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ProductionOrderVO">
		select
		<include refid="Base_Column_List" />,cim.entrusted_unit as entrustedUnit,pl.product_model as productModel,pl.manufacturer as manufacturer,
            pl.product_name as productName,pl.production_batch as prodctionBatch,t.quantity as quantity ,
            pl.product_category as productCategory,ss.specification_number as  standardSpecificationNumber,
            pl.deadline as deadline,pl.task_level as taskLevel,su.user_name  as responsiblePerson,pl.test_type as testType
			from production_order t left join evaluation_order eo on t.order_number =eo.order_number
            left join customer_information_management cim on eo.customer_id  = cim.id
            left join product_list pl on t.product  = pl.id
            left join standard_specification ss on pl.standard_specification_id = ss.id
            left join sys_user su on t.responsible_person  = su.id
            <where>
                and t.codex_torch_deleted = '0'
                <if test="workOrderNumber != null and workOrderNumber != ''">
                    and t.work_order_number  like concat('%', #{workOrderNumber} ,'%')
                </if>
                <if test="orderNumber != null and orderNumber != ''">
                    and eo.id  = #{orderNumber}
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and cim.id  = #{entrustedUnit}
                </if>

                <if test="productModel != null and productModel != ''">
                    and pl.product_model  = #{productModel}
                </if>
                <if test="productName != null and productName != ''">
                    and pl.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="productionBatch != null and productionBatch != ''">
                    and t.production_batch  like concat('%', #{productionBatch} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and pl.manufacturer  like concat('%', #{manufacturer} ,'%')
                </if>
                <if test="productCategory != null and productCategory != ''">
                    and pl.product_category  like concat('%', #{productCategory} ,'%')
                </if>
                <if test="taskLevel != null and taskLevel != ''">
                    and pl.task_level  = #{taskLevel}
                </if>
                <if test="responsiblePerson  != null and responsiblePerson != ''">
                    and t.responsible_person  = #{responsiblePerson}
                </if>

                <if test="standardSpecificationNumber != null and standardSpecificationNumber != ''">
                    and pl.standard_specification_number  like concat('%', #{standardSpecificationNumber} ,'%')
                </if>

                <if test="testType != null and testType != ''">
                    and pl.test_type  like concat('%', #{testType} ,'%')
                </if>
                <if test="deadlineStart != null">
                    and pl.deadline  &gt;= #{deadlineStart}
                </if>
                <if test="deadlineEnd != null">
                    and pl.deadline  &lt;= #{deadlineEnd}
                </if>
                <if test="testMethodology != null and testMethodology != ''">
                    and t.test_methodology  = #{testMethodology}
                </if>

                <if test="workOrderStatus != null and workOrderStatus != ''">
                    and t.work_order_status  = #{workOrderStatus}
                </if>

                <if test="productionStage != null and productionStage != ''">
                    and t.production_stage  = #{productionStage}
                </if>

                <if test="completionTime != null">
                    and t.completion_time  = #{completionTime}
                </if>
                <if test="wmfcnr != null and wmfcnr != ''">
                    and t.wmfcnr  = #{wmfcnr}
                </if>
                <if test="irretrievableReason != null and irretrievableReason != ''">
                    and t.irretrievable_reason  = #{irretrievableReason}
                </if>
                <if test="codexTorchApprovalStatus != null and codexTorchApprovalStatus != ''">
                    and (t.codex_torch_approval_status  like concat('%', #{codexTorchApprovalStatus} ,'%')
                    or t.codex_torch_approval_status  REGEXP #{codexTorchApprovalStatus})
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>

                ${params.dataScope}
            </where>
        order by t.work_order_status
	</select>
     <select id="selectOptionsByOrderNumber" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.order_number label,
        	t.id value
        from evaluation_order t
        WHERE  t.codex_torch_deleted = '0'
     </select>
     <select id="selectDataLinkageByOrderNumber" parameterType="String"
             resultType="java.util.Map">
        select
            t.engineering_code as engineeringCode,
            t.order_remarks as orderRemarks
        from evaluation_order t
        WHERE t.order_number = #{order_number}
            and t.codex_torch_deleted = '0'
     </select>
     <select id="selectOptionsByEntrustedUnit" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.entrusted_unit label,
        	t.id value
        from customer_information_management t
        WHERE t.entrusted_unit != '' and t.codex_torch_deleted = '0'
     </select>
     <select id="selectOptionsByProductModel" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
         select
         distinct
         t.product_model  label,
         t.product_model value
         from product_list t
         WHERE  t.codex_torch_deleted = '0' and t.product_model is not null
     </select>
     <select id="selectDataLinkageByProductModel" parameterType="String"
             resultType="java.util.Map">
        select
            t.product_name as productName,
            t.production_batch as productionBatch,
            t.manufacturer as manufacturer,
            t.inspection_quantity2 as inspectionQuantity2,
            t.sample_total_count as sampleTotalCount,
            t.standard_specification_number as standardSpecificationNumber,
            t.experiment_project as experimentProject,
            t.work_order_inspection_number1 as workOrderInspectionNumber1,
            t.quality_grade as qualityGrade,
            t.test_type as testType,
            t.deadline7 as deadline7
        from product_list1 t
        WHERE t.product_model = #{product_model}
            and t.codex_torch_deleted = '0'
     </select>
     <select id="selectOptionsByPredecessorWorkOrder" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
             t.work_order_number  label,
             t.id value
         from production_order t
         WHERE  t.codex_torch_deleted = '0'
     </select>
     <select id="selectOptionsByRelatedWorkOrder" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
             t.work_order_number  label,
             t.id value
         from production_order t
         WHERE  t.codex_torch_deleted = '0'
     </select>
     <select id="selectOptionsByResponsiblePerson" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.user_name label,
        	t.id value
        from sys_user t
        WHERE  t.deleted = '0'
     </select>

    <select id="selectAwaitingProductionOrderList" parameterType="com.huatek.frame.modules.business.service.dto.ProductionOrderDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ProductionOrderVO">
		select
		<include refid="Base_Column_List" />,cim.entrusted_unit as entrustedUnit,pl.product_model as productModel,pl.manufacturer as manufacturer,
        pl.product_name as productName,pl.production_batch as prodctionBatch,t.quantity as quantity ,
        pl.product_category as productCategory,ss.specification_number as  standardSpecificationNumber,
        pl.deadline as deadline,pl.task_level as taskLevel,su.user_name  as responsiblePerson
        from production_order t left join evaluation_order eo on t.order_number =eo.order_number
        left join customer_information_management cim on eo.customer_id  = cim.id
        left join product_list pl on t.product  = pl.id
        left join standard_specification ss on pl.standard_specification_id = ss.id
        left join sys_user su on t.responsible_person  = su.id
            <where>
                and t.codex_torch_deleted = '0'
                <if test="workOrderNumber != null and workOrderNumber != ''">
                    and t.work_order_number  like concat('%', #{workOrderNumber} ,'%')
                </if>
                <if test="orderNumber != null and orderNumber != ''">
                    and eo.id  = #{orderNumber}
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and t.entrusted_unit  = #{entrustedUnit}
                </if>
                <if test="engineeringCode != null and engineeringCode != ''">
                    and t.engineering_code  like concat('%', #{engineeringCode} ,'%')
                </if>
                <if test="orderRemarks != null and orderRemarks != ''">
                    and t.order_remarks  = #{orderRemarks}
                </if>
                <if test="reportRequirements != null and reportRequirements != ''">
                    and t.report_requirements  = #{reportRequirements}
                </if>
                <if test="reportFormat != null and reportFormat != ''">
                    and t.report_format REGEXP #{reportFormat}
                </if>
                <if test="dataReqERep != null and dataReqERep != ''">
                    and t.data_req_e_rep  = #{dataReqERep}
                </if>
                <if test="dataReqsPapereport != null and dataReqsPapereport != ''">
                    and t.data_reqs_papereport  = #{dataReqsPapereport}
                </if>
                <if test="productModel != null and productModel != ''">
                    and t.product_model  = #{productModel}
                </if>
                <if test="productName != null and productName != ''">
                    and t.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="productionBatch != null and productionBatch != ''">
                    and t.production_batch  like concat('%', #{productionBatch} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and t.manufacturer  like concat('%', #{manufacturer} ,'%')
                </if>
                <if test="inspectionQuantity2 != null and inspectionQuantity2 != ''">
                    and t.inspection_quantity2  = #{inspectionQuantity2}
                </if>
                <if test="sampleTotalCount != null and sampleTotalCount != ''">
                    and t.sample_total_count  = #{sampleTotalCount}
                </if>
                <if test="taskLevel != null and taskLevel != ''">
                    and t.task_level  = #{taskLevel}
                </if>
                <if test="standardSpecificationNumber != null and standardSpecificationNumber != ''">
                    and t.standard_specification_number  like concat('%', #{standardSpecificationNumber} ,'%')
                </if>
                <if test="experimentProject != null and experimentProject != ''">
                    and t.experiment_project  like concat('%', #{experimentProject} ,'%')
                </if>
                <if test="workOrderInspectionNumber1 != null and workOrderInspectionNumber1 != ''">
                    and t.work_order_inspection_number1  like concat('%', #{workOrderInspectionNumber1} ,'%')
                </if>
                <if test="packageForm != null and packageForm != ''">
                    and t.package_form  like concat('%', #{packageForm} ,'%')
                </if>
                <if test="qualityGrade != null and qualityGrade != ''">
                    and t.quality_grade  like concat('%', #{qualityGrade} ,'%')
                </if>
                <if test="testType != null and testType != ''">
                    and t.test_type  like concat('%', #{testType} ,'%')
                </if>
                <if test="deadline7 != null">
                    and t.deadline7  = #{deadline7}
                </if>
                <if test="testMethodology != null and testMethodology != ''">
                    and t.test_methodology  = #{testMethodology}
                </if>
                <if test="predecessorWorkOrder != null and predecessorWorkOrder != ''">
                    and t.predecessor_work_order  = #{predecessorWorkOrder}
                </if>
                <if test="relatedWorkOrder != null and relatedWorkOrder != ''">
                    and t.related_work_order  = #{relatedWorkOrder}
                </if>
                <if test="pda != null and pda != ''">
                    and t.pda  = #{pda}
                </if>
                <if test="whetherToIncludeInScheduling != null and whetherToIncludeInScheduling != ''">
                    and t.whether_to_include_in_scheduling  = #{whetherToIncludeInScheduling}
                </if>
                <if test="estimatedCompletionTime != null">
                    and t.estimated_completion_time  = #{estimatedCompletionTime}
                </if>
                <if test="quantity != null and quantity != ''">
                    and t.quantity  = #{quantity}
                </if>
                <if test="attachment != null and attachment != ''">
                    and t.attachment  = #{attachment}
                </if>
                <if test="workOrderStatus != null and workOrderStatus != ''">
                    and t.work_order_status  = #{workOrderStatus}
                </if>
                <if test="responsiblePerson != null and responsiblePerson != ''">
                    and t.responsible_person  = #{responsiblePerson}
                </if>
                <if test="productionStage != null and productionStage != ''">
                    and t.production_stage  = #{productionStage}
                </if>
                <if test="wtstabr != null and wtstabr != ''">
                    and t.wtstabr  = #{wtstabr}
                </if>
                <if test="whetherToEnterComponents != null and whetherToEnterComponents != ''">
                    and t.whether_to_enter_components  = #{whetherToEnterComponents}
                </if>
                <if test="whetherToEnterDocuments != null and whetherToEnterDocuments != ''">
                    and t.whether_to_enter_documents  = #{whetherToEnterDocuments}
                </if>
                <if test="completionTime8 != null">
                    and t.completion_time8  = #{completionTime8}
                </if>
                <if test="wmfcnr != null and wmfcnr != ''">
                    and t.wmfcnr  = #{wmfcnr}
                </if>
                <if test="irretrievableReason != null and irretrievableReason != ''">
                    and t.irretrievable_reason  = #{irretrievableReason}
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectAwaitingProductionOrderListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.ProductionOrderVO">
		select
		<include refid="Base_Column_List" />,cim.entrusted_unit as entrustedUnit,pl.product_model as productModel,pl.manufacturer as manufacturer,
        pl.product_name as productName,pl.production_batch as prodctionBatch,t.quantity as quantity ,
        pl.product_category as productCategory,ss.specification_number as  standardSpecificationNumber,
        pl.deadline as deadline,pl.task_level as taskLevel,su.user_name  as responsiblePerson
        from production_order t left join evaluation_order eo on t.order_number =eo.order_number
        left join customer_information_management cim on eo.customer_id  = cim.id
        left join product_list pl on t.product  = pl.id
        left join standard_specification ss on pl.standard_specification_id = ss.id
        left join sys_user su on t.responsible_person  = su.id
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>

	<select id="selectProductionOrderByModelAndBatch"
            resultType="com.huatek.frame.modules.business.domain.ProductionOrder">
        select
        <include refid="Base_Column_List" />
        from production_order t
        left join product_list pl on t.product  = pl.id
        where pl.evaluation_order_id =#{evaluationOrderId}
        and pl.product_model = #{productModel}
        and pl.production_batch =#{productionBatch} order by t.work_order_number desc limit 1
    </select>

	<select id="selectProductionOrderById"
            resultType="com.huatek.frame.modules.business.domain.vo.ProductionOrderVO">
        select
        t.id,
        t.order_number as orderNumber, cim.entrusted_unit as entrustedUnit, eo.engineering_code as engineeringCode,
        eo.comment as orderRemarks, eo.report_requirements as reportRequirements, eo.report_format as reportFormat,
        eo.data_req_e_rep as dataReqERep, eo.data_reqs_papereport as dataReqsPapereport,
        pl.product_model as productModel , pl.product_name as productName,
        pl.production_batch as productionBatch, pl.manufacturer ,
        pl.quantity as inspectionQuantity, pl.sample_total_count as sampleTotalCount,
        pl.quality_grade as qualityGrade, pl.product_category as productCategory,
        pl.experiment_project as experimentProject, pl.work_order_inspection_number as workOrderInspectionNumber,
        ss.specification_number as standardSpecificationNumber, pl.task_level as taskLevel,
        pl.test_type as testType, pl.deadline , t.work_order_number as workOrderNumber,
        t.test_methodology as testMethodology, t.predecessor_work_order as predecessorWorkOrder,
        t.related_work_order as relatedWorkOrder,  t.pda ,
        t.whether_to_include_in_scheduling as whetherToIncludeInScheduling, t.estimated_completion_time as estimatedCompletionTime,
        t.quantity , t.attachment,su.user_name  as responsiblePerson,t.production_stage as productionStage,t.work_order_status as workOrderStatus
        from production_order t
        left join evaluation_order eo on t.order_number =eo.order_number
        left join customer_information_management cim on eo.customer_id  = cim.id
        left join product_list pl on t.product  = pl.id
        left join standard_specification ss on pl.standard_specification_id = ss.id
        left join sys_user su on t.responsible_person  = su.id
        where t.id =#{id}

    </select>

	<select id="selectSameModelBatchManufacture"
            resultType="com.huatek.frame.modules.business.domain.vo.ProductionOrderVO">
        select t.id,t.work_order_number ,pl.product_model ,pl.production_batch ,pl.manufacturer
        from production_order t left join product_list pl on t.product =pl.id
        where pl.product_model =#{productModel} and pl.production_batch =#{productionBatch}
         and pl.manufacturer =#{manufacturer} and pl.codex_torch_group_id =#{codexTorchGroupId} and t.id !=#{productionOrderId}
         and t.work_order_status ='7'
    </select>

	<select id="selectCurrentUserRoles" resultType="java.lang.String">
        select sr.role from sys_user_role sur left join sys_role sr on sur.role_id  = sr.id where sur.user_id =#{currentUserId}
    </select>

    <select id="selectOptionsByProcessCode3" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.process_name2 label,
        t.id value
        from standard_process_management t
        WHERE t.step_number != ''
    </select>
    <select id="selectOptionsByAssoWoPredProc" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.process_name2 label,
        t.id value
        from standard_process_management t
        WHERE t.step_number != ''
    </select>
    <select id="selectOptionsByWorkstation" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.workstation_name label,
        t.id value
        from workstation t
        WHERE t.workstation_number != ''
    </select>
    <select id="selectOptionsByProductInformation1" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.specification_name label,
        t.id value
        from standard_specification t
        WHERE t.specification_number != ''
    </select>
    <select id="selectOptionsByTestingTeam" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.group_name label,
        t.id value
        from sys_group t
        WHERE t.group_code != ''
    </select>
    <select id="selectOptionsByDeviceType" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.device_type_name label,
        t.device_type_name value
        from device_type t
        WHERE t.device_type_name != ''
    </select>

</mapper>