<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.EvaluationOrderNotifyMapper">
    <sql id="Base_Column_List">
        t.id as id,
        t.evaluation_order_id as evaluationOrderId,
        t.product_list_id as productListId,
        t.message as message,
        t.recipient as recipient,
        t.creator as creator,
        t.create_time as createTime,
        t.deleted as deleted
    </sql>
    <select id="findAll" resultType="com.huatek.frame.modules.business.domain.vo.EvaluationOrderNotifyVO">
        select
        <include refid="Base_Column_List"/>, e.order_number as orderNumber
        from evaluation_order_notify t
        LEFT JOIN evaluation_order e ON e.id = t.evaluation_order_id
        <where>
            t.deleted = '0'
            <if test="evaluationOrderId != null and evaluationOrderId != ''">
                and t.evaluation_order_id like concat ('%', #{evaluationOrderId}, '%')
            </if>
        </where>
    </select>


</mapper>