package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Date;
import java.sql.Timestamp;

/**
 * 测评订单通知信息实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("evaluation_order_notify")
public class EvaluationOrderNotify {
    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID) // 假设使用UUID作为主键生成策略
    private String id;

    /**
     * 测评订单id
     */
    @TableField("evaluation_order_id")
    private String evaluationOrderId;

    /**
     * 产品列表id
     */
    @TableField("product_list_id")
    private String productListId;

    /**
     * 变更通知消息
     */
    @TableField("message")
    private String message;

    /**
     * 接收人id
     */
    @TableField("recipient")
    private String recipient;

    /**
     * 消息发送人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Timestamp createTime;

    /**
     * 删除标识 (0-未删除，1-已删除)
     */
    @TableField("deleted")
    private String deleted;

}
