package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.sql.Date;

import lombok.Getter;
import lombok.Setter;

/**
* @description 试验项目
* <AUTHOR>
* @date 2025-07-17
**/
@Setter
@Getter
@TableName("experiment_project")
public class ExperimentProject implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    
    /**
	 * 工序方案名称
     **/
    @TableField(value = "process_scheme_name"
    )
    private String processSchemeName;

    
    /**
	 * 显示序号
     **/
    @TableField(value = "display_number"
    )
    private Long displayNumber;

    /**
     * 工序id
     **/
    @TableField(value = "process_id"
    )
    private String processId;
    
    /**
	 * 工序编码
     **/
    @TableField(value = "process_code3"
    )
    private String processCode3;

    
    /**
	 * 客户工序名称
     **/
    @TableField(value = "customer_process_name"
    )
    private String customerProcessName;

    
    /**
	 * 执行顺序
     **/
    @TableField(value = "execution_sequence"
    )
    private Long executionSequence;

    
    /**
	 * 关联工单前置工序
     **/
    @TableField(value = "asso_wo_pred_proc"
    )
    private String assoWoPredProc;

    
    /**
	 * 试验条件
     **/
    @TableField(value = "test_conditions"
    )
    private String testConditions;

    /**
     * 试验依据
     **/
    @TableField(value = "test_basis"
    )
    private String testBasis;

    
    /**
	 * 试验次数
     **/
    @TableField(value = "testing_times"
    )
    private String testingTimes;

    
    /**
	 * 试验时长
     **/
    @TableField(value = "duration_of_testing"
    )
    private Long durationOfTesting;

    
    /**
	 * 判定依据
     **/
    @TableField(value = "judgment_criteria"
    )
    private String judgmentCriteria;



    
    /**
	 * 工作站
     **/
    @TableField(value = "workstation"
    )
    private String workstation;

    
    /**
	 * 设备类型
     **/
    @TableField(value = "device_type"
    )
    private String deviceType;

    
    /**
	 * 产品资料
     **/
    @TableField(value = "product_information1"
    )
    private String productInformation1;

    
    /**
	 * 试验方式
     **/
    @TableField(value = "test_methodology"
    )
    private String testMethodology;

    
    /**
	 * 试验班组
     **/
    @TableField(value = "testing_team"
    )
    private String testingTeam;

    
    /**
	 * PDA
     **/
    @TableField(value = "pda"
    )
    private Long pda;

    
    /**
	 * 组别
     **/
    @TableField(value = "`grouping`"
    )
    private String grouping;

    
    /**
	 * 样品数量
     **/
    @TableField(value = "sample_quantity12"
    )
    private String sampleQuantity12;

    
    /**
	 * 是否加入排产
     **/
    @TableField(value = "whether_to_include_in_scheduling"
    )
    private String whetherToIncludeInScheduling;

    
    /**
	 * 主表单ID
     **/
    @TableField(value = "CODEX_TORCH_MASTER_FORM_ID"
    )
    private String codexTorchMasterFormId;

    
    /**
	 * 创建人
     **/
    @TableField(value = "CODEX_TORCH_CREATOR_ID"
    )
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @TableField(value = "CODEX_TORCH_UPDATER"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;

    
    /**
	 * 所属组织
     **/
    @TableField(value = "CODEX_TORCH_GROUP_ID"
    )
    private String codexTorchGroupId;

    
    /**
	 * 创建时间
     **/
    @TableField(value = "CODEX_TORCH_CREATE_DATETIME"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "CODEX_TORCH_UPDATE_DATETIME"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @TableField(value = "CODEX_TORCH_DELETED"
    )
    private String codexTorchDeleted;
}
