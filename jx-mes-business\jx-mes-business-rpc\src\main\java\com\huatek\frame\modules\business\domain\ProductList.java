package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.sql.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
* @description 产品列表
* <AUTHOR>
* @date 2025-07-30
**/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("product_list")
public class ProductList implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    
    /**
	 * 测评订单id
     **/
    @TableField(value = "evaluation_order_id"
    )
    private String evaluationOrderId;

    /**
     * 产品管理id
     */
    @TableField(value = "product_id")
    private String productId;

    /**
     * 标准/规范编号id
     */
    @TableField(value = "standard_specification_id")
    private String standardSpecificationId;

    /**
	 * 序号
     **/
    @TableField(value = "serial_number"
    )
    private Integer serialNumber;

    /**
     * 产品型号
     */
    @TableField(value = "product_model")
    private String productModel;

    /**
     * 生产厂家
     */
    @TableField(value = "manufacturer")
    private String manufacturer;

    /**
     * 产品分类
     */
    @TableField(value = "product_category")
    private String productCategory;

    /**
     * 产品名称
     */
    @TableField(value = "product_name")
    private String productName;

    /**
	 * 生产批次
     **/
    @TableField(value = "production_batch"
    )
    private String productionBatch;

    /**
	 * 数量
     **/
    @TableField(value = "quantity"
    )
    private Integer quantity;

    
    /**
	 * 任务等级
     **/
    @TableField(value = "task_level"
    )
    private String taskLevel;

    
    /**
	 * 试验类型
     **/
    @TableField(value = "test_type"
    )
    private String testType;

    
    /**
	 * 工单送检编号
     **/
    @TableField(value = "work_order_inspection_number"
    )
    private String workOrderInspectionNumber;

    
    /**
	 * 质量等级
     **/
    @TableField(value = "quality_grade"
    )
    private String qualityGrade;

    
    /**
	 * 样本总数
     **/
    @TableField(value = "sample_total_count"
    )
    private Integer sampleTotalCount;

    
    /**
	 * 试验项目
     **/
    @TableField(value = "experiment_project"
    )
    private String experimentProject;

    
    /**
	 * 专项分析试验项目
     **/
    @TableField(value = "special_analysis_test_project"
    )
    private String specialAnalysisTestProject;

    
    /**
	 * 组别类型
     **/
    @TableField(value = "group_type"
    )
    private String groupType;

    
    /**
	 * 鉴定试验项目
     **/
    @TableField(value = "inspection_test_project"
    )
    private String inspectionTestProject;

    
    /**
	 * 质量一致性试验项目
     **/
    @TableField(value = "quality_consistency_test_items"
    )
    private String qualityConsistencyTestItems;

    
    /**
	 * DPA试验项目
     **/
    @TableField(value = "dpa_test_project"
    )
    private String dpaTestProject;

    
    /**
	 * 其他试验
     **/
    @TableField(value = "other_tests"
    )
    private String otherTests;

    
    /**
	 * 技术对接姓名
     **/
    @TableField(value = "technical_liaison_name"
    )
    private String technicalLiaisonName;

    
    /**
	 * 技术对接人电话
     **/
    @TableField(value = "technical_liaison_phone_number"
    )
    private String technicalLiaisonPhoneNumber;

    
    /**
	 * 要求完成日期
     **/
    @TableField(value = "deadline"
    )
    private Date deadline;

    
    /**
	 * 状态
     **/
    @TableField(value = "status"
    )
    private String status;

    
    /**
	 * 退回/不可筛原因
     **/
    @TableField(value = "reject_unscreenable_reason"
    )
    private String rejectUnscreenableReason;


    
    /**
	 * 创建人
     **/
    @TableField(value = "codex_torch_creator_id"
    )
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @TableField(value = "codex_torch_updater"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;

    
    /**
	 * 所属组织
     **/
    @TableField(value = "codex_torch_group_id"
    )
    private String codexTorchGroupId;

    
    /**
	 * 创建时间
     **/
    @TableField(value = "codex_torch_create_datetime"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "codex_torch_update_datetime"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @TableField(value = "codex_torch_deleted"
    )
    private String codexTorchDeleted;
}