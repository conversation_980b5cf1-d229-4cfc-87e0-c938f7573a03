package com.huatek.frame.modules.business.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huatek.frame.common.annotation.poi.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.sql.Date;
import java.sql.Timestamp;

@Data
public class ProductListExportVO {

    /**
     * 标准规范编号
     */
    @Excel(name = "标准规范编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    @ApiModelProperty("标准/规范编号")
    private String specificationNumberId;

    /**
     * 序号
     **/
    @ApiModelProperty("序号")
    @Excel(name = "序号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Long serialNumber;

    /**
     * 产品型号
     */
    @Excel(name = "产品型号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    @ApiModelProperty("产品型号")
    private String productModel;

    /**
     * 产品名称
     */
    @Excel(name = "产品名称",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 生产批次
     **/
    @ApiModelProperty("生产批次")
    @Excel(name = "生产批次",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productionBatch;

    /**
     * 生产厂家
     */
    @ApiModelProperty("生产厂家")
    @Excel(name = "生产厂家",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String manufacturer;


    /**
     * 数量
     **/
    @ApiModelProperty("数量")
    @Excel(name = "数量",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Long quantity;


    /**
     * 任务等级
     **/
    @ApiModelProperty("任务等级")
    @Excel(name = "任务等级",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String taskLevel;

    /**
     * 试验类型
     **/
    @ApiModelProperty("试验类型")
    @Excel(name = "试验类型",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String testType;

    /**
     * 工单送检编号
     **/
    @ApiModelProperty("工单送检编号")
    @Excel(name = "工单送检编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String workOrderInspectionNumber;

    /**
     * 质量等级
     **/
    @ApiModelProperty("质量等级")
    @Excel(name = "质量等级",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String qualityGrade;

    /**
     * 样本总数
     **/
    @ApiModelProperty("样本总数")
    @Excel(name = "样本总数",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Long sampleTotalCount;


    /**
     * 试验项目
     **/
    @ApiModelProperty("试验项目")
    @Excel(name = "试验项目",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String experimentProject;

    /**
     * 专项分析试验项目
     **/
    @ApiModelProperty("专项分析试验项目")
    @Excel(name = "专项分析试验项目",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String specialAnalysisTestProject;

    /**
     * 组别类型
     **/
    @ApiModelProperty("组别类型")
    @Excel(name = "组别类型",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String groupType;

    /**
     * 鉴定试验项目
     **/
    @ApiModelProperty("鉴定试验项目")
    @Excel(name = "鉴定试验项目",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String inspectionTestProject;

    /**
     * 质量一致性试验项目
     **/
    @ApiModelProperty("质量一致性试验项目")
    @Excel(name = "质量一致性试验项目",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String qualityConsistencyTestItems;

    /**
     * DPA试验项目
     **/
    @ApiModelProperty("DPA试验项目")
    @Excel(name = "DPA试验项目",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String dpaTestProject;

    /**
     * 其他试验
     **/
    @ApiModelProperty("其他试验")
    @Excel(name = "其他试验",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String otherTests;

    /**
     * 技术对接姓名
     **/
    @ApiModelProperty("技术对接人姓名")
    @Excel(name = "技术对接人姓名",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String technicalLiaisonName;

    /**
     * 技术对接人电话
     **/
    @ApiModelProperty("技术对接人电话")
    @Excel(name = "技术对接人电话",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String technicalLiaisonPhoneNumber;

    /**
     * 要求完成日期
     **/
    @ApiModelProperty("要求完成日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "要求完成日期",
            cellType = Excel.ColumnType.NUMERIC,
            dateFormat = "yyyy-MM-dd",
            type = Excel.Type.ALL)
    private Date deadline;

    /**
     * 状态
     **/
    @ApiModelProperty("状态")
    @Excel(name = "状态",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String status;




}
