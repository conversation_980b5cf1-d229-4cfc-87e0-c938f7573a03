package com.huatek.frame.modules.business.domain.vo;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @description 工单工序试验数据
* <AUTHOR>
* @date 2025-07-30
**/
@Data
@ApiModel("工单工序试验数据VO实体类")
public class ProductionOrderProcessTestVO implements Serializable {


    @ApiModelProperty("主键ID")
    private String id;

    
    /**
	 * 工单
     **/
    @ApiModelProperty("工单")
    private String workOrder;

    
    /**
	 * 工序
     **/
    @ApiModelProperty("工序")
    private String processCode;

    /**
     * 工序报工内容
     **/
    @ApiModelProperty("工序报工内容")
    private String processData;

    /**
     * 设备数据
     **/
    @ApiModelProperty("设备数据")
    private String deviceData;
    /**
	 * 试验结果
     **/
    @ApiModelProperty("试验结果")
    private String testData;



    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;

    

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;
}