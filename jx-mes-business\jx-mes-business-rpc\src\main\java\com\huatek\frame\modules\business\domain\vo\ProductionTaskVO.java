package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.service.dto.ProdTaskEqInfoDTO;
import com.huatek.frame.modules.business.service.dto.ProdTaskOpHistDTO;
import com.huatek.frame.modules.business.service.dto.ProductionTaskAttachmentsDTO;
import com.huatek.frame.modules.business.service.dto.ProductionTaskTestDataDTO;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.sql.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 生产任务VO实体类
 * @date 2025-08-11
 **/
@Data
@ApiModel("生产任务DTO实体类")
public class ProductionTaskVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 任务编号
     **/
    @ApiModelProperty("任务编号")
    @Excel(name = "任务编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String taskNumber;

    /**
     * 工单编号
     **/
    @ApiModelProperty("工单编号")
    @Excel(name = "工单编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String workOrderNumber;

    /**
     * 执行顺序
     **/
    @ApiModelProperty("执行顺序")
    @Excel(name = "执行顺序",
            cellType = Excel.ColumnType.NUMERIC,
            type = Excel.Type.ALL)
    private Integer executionSequence;

    /**
     * 关联工单前置工序
     **/
    @ApiModelProperty("关联工单前置工序")
    @Excel(name = "关联工单前置工序",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String assoWoPredProc;

    /**
     * 关联工单
     **/
    @ApiModelProperty("关联工单")
    @Excel(name = "关联工单",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String relatedWorkOrder;

    /**
     * 工作站
     **/
    @ApiModelProperty("工作站")
    @Excel(name = "工作站",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String workstation;

    /**
     * 送检数量
     **/
    @ApiModelProperty("送检数量")
    @Excel(name = "送检数量",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Integer inspectionQuantity2;

    /**
     * 工序名称
     **/
    @ApiModelProperty("工序名称")
    @Excel(name = "工序名称",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String processName2;

    /**
     * 工序编码
     **/
    @ApiModelProperty("工序编码")
    @Excel(name = "工序编码",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String processCode;

    /**
     * 试验依据
     **/
    @ApiModelProperty("试验依据")
    @Excel(name = "试验依据",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String testBasis;

    /**
     * 试验条件
     **/
    @ApiModelProperty("试验条件")
    @Excel(name = "试验条件",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String testConditions;

    /**
     * 判定依据
     **/
    @ApiModelProperty("判定依据")
    @Excel(name = "判定依据",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String judgmentCriteria;

    /**
     * 状态
     **/
    @ApiModelProperty("状态")
    @Excel(name = "状态",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String status;

    /**
     * 技术能力编号
     **/
    @ApiModelProperty("技术能力编号")
    @Excel(name = "技术能力编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String technicalCompetencyNumber;

    /**
     * 操作卡
     **/
    @ApiModelProperty("操作卡")
    @Excel(name = "操作卡",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String operationCard;

    /**
     * 备注
     **/
    @ApiModelProperty("备注")
    @Excel(name = "备注",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String comment;

    /**
     * 工单等级
     **/
    @ApiModelProperty("工单等级")
    @Excel(name = "工单等级",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String ticketLevel;

    /**
     * 计划开始时间
     **/
    @ApiModelProperty("计划开始时间")
    @Excel(name = "计划开始时间",
            cellType = Excel.ColumnType.NUMERIC,
            dateFormat = "yyyy-MM-dd HH:mm:ss",
            type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp scheduledStartTime;

    /**
     * 计划结束时间
     **/
    @ApiModelProperty("计划结束时间")
    @Excel(name = "计划结束时间",
            cellType = Excel.ColumnType.NUMERIC,
            dateFormat = "yyyy-MM-dd HH:mm:ss",
            type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp scheduledEndTime;

    /**
     * 产品名称
     **/
    @ApiModelProperty("产品名称")
    @Excel(name = "产品名称",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productName;

    /**
     * 产品型号
     **/
    @ApiModelProperty("产品型号")
    @Excel(name = "产品型号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productModel;

    /**
     * 生产厂家
     **/
    @ApiModelProperty("生产厂家")
    @Excel(name = "生产厂家",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String manufacturer;

    /**
     * 批次号
     **/
    @ApiModelProperty("批次号")
    @Excel(name = "批次号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String batchNumber;

    /**
     * 产品分类
     **/
    @ApiModelProperty("产品分类")
    @Excel(name = "产品分类",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productCategory;

    /**
     * 产品资料
     **/
    @ApiModelProperty("产品资料")
    @Excel(name = "产品资料",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productInformation1;

    /**
     * 委托单位
     **/
    @ApiModelProperty("委托单位")
    @Excel(name = "委托单位",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String entrustedUnit;

    /**
     * 组别
     **/
    @ApiModelProperty("组别")
    @Excel(name = "组别",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String grouping;

    /**
     * 试验方式
     **/
    @ApiModelProperty("试验方式")
    @Excel(name = "试验方式",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String testMethodology;

    /**
     * 实际开始时间
     **/
    @ApiModelProperty("实际开始时间")
    @Excel(name = "实际开始时间",
            cellType = Excel.ColumnType.NUMERIC,
            dateFormat = "yyyy-MM-dd HH:mm:ss",
            type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp actualStartTime;

    /**
     * 实际结束时间
     **/
    @ApiModelProperty("实际结束时间")
    @Excel(name = "实际结束时间",
            cellType = Excel.ColumnType.NUMERIC,
            dateFormat = "yyyy-MM-dd HH:mm:ss",
            type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp actualEndTime;

    /**
     * 试验类型
     **/
    @ApiModelProperty("试验类型")
    @Excel(name = "试验类型",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String testType;

    /**
     * 所属部门
     **/
    @ApiModelProperty("所属部门")
    @Excel(name = "所属部门",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String department;

    /**
     * 所属班组
     **/
    @ApiModelProperty("所属班组")
    @Excel(name = "所属班组",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String belongingTeam2;

    /**
     * 报工时间
     **/
    @ApiModelProperty("报工时间")
    @Excel(name = "报工时间",
            cellType = Excel.ColumnType.NUMERIC,
            dateFormat = "yyyy-MM-dd HH:mm:ss",
            type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp reportingTime0;

    /**
     * PDA预警
     **/
    @ApiModelProperty("PDA预警")
    @Excel(name = "PDA预警",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String pdaWarning;

    /**
     * 报工人
     **/
    @ApiModelProperty("报工人")
    @Excel(name = "报工人",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String reporter4;

    /**
     * 不合格编号
     **/
    @ApiModelProperty("不合格编号")
    @Excel(name = "不合格编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String nonConformityNumber;

    /**
     * 合格数量
     **/
    @ApiModelProperty("合格数量")
    @Excel(name = "合格数量",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Integer qualifiedQuantity;

    /**
     * 不合格数量
     **/
    @ApiModelProperty("不合格数量")
    @Excel(name = "不合格数量",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Integer unqualifiedQuantity;

    /**
     * 失效模式
     **/
    @ApiModelProperty("失效模式")
    @Excel(name = "失效模式",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String failureMode;

    /**
     * 完成时间
     **/
    @ApiModelProperty("完成时间")
    @Excel(name = "完成时间",
            cellType = Excel.ColumnType.NUMERIC,
            dateFormat = "yyyy-MM-dd",
            type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date completionTime6;

    /**
     * PDA
     **/
    @ApiModelProperty("PDA")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    @Excel(name = "PDA",
            cellType = Excel.ColumnType.NUMERIC,
            type = Excel.Type.ALL)
    private BigDecimal pda;

    /**
     * 记录更改盖章
     **/
    @ApiModelProperty("记录更改盖章")
    @Excel(name = "记录更改盖章",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Long recordChangeStamp;

    /**
     * 湿度
     **/
    @ApiModelProperty("湿度")
    @Excel(name = "湿度",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String humidity;

    /**
     * 温度
     **/
    @ApiModelProperty("温度")
    @Excel(name = "温度",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String temperature;

    /**
     * 试验结果总结
     **/
    @ApiModelProperty("试验结果总结")
    @Excel(name = "试验结果总结",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String testResultSummary;

    /**
     * 报工备注
     **/
    @ApiModelProperty("报工备注")
    @Excel(name = "报工备注",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String reportWorkRemarks;

    /**
     * 附件
     **/
    @ApiModelProperty("附件")
    @Excel(name = "附件",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String attachment;

    /**
     * 关联异常反馈编号
     **/
    @ApiModelProperty("关联异常反馈编号")
    @Excel(name = "关联异常反馈编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String assocExceptionFeedbackNum;

    /**
     * 暂停原因
     **/
    @ApiModelProperty("暂停原因")
    @Excel(name = "暂停原因",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String pauseReason;

    /**
     * 已完成数量
     **/
    @ApiModelProperty("已完成数量")
    @Excel(name = "已完成数量",
            cellType = Excel.ColumnType.NUMERIC,
            type = Excel.Type.ALL)
    private Integer completedQuantity;

    /**
     * 客户工序名称
     **/
    @ApiModelProperty("客户工序名称")
    @Excel(name = "客户工序名称",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String customerProcessName;

    /**
     * 显示序号
     **/
    @ApiModelProperty("显示序号")
    @Excel(name = "显示序号",
            cellType = Excel.ColumnType.NUMERIC,
            type = Excel.Type.ALL)
    private Integer displayNumber;

    /**
     * 负责人
     **/
    @ApiModelProperty("负责人")
    @Excel(name = "负责人",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String responsiblePerson;

    /**
     * 生成报表
     **/
    @ApiModelProperty("生成报表")
    private String generateReport;

    /**
     * 订单编号
     **/
    @ApiModelProperty("订单编号")
    private String orderNumber;

    /**
     * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
     * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.EXPORT)
    private String codexTorchUpdater;

    /**
     * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
     * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
     * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
     * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;

    @ApiModelProperty("试验数据")
    private List<ProductionTaskTestDataVO> productionTaskTestDataList;

    @ApiModelProperty("设备信息")
    private List<ProdTaskEqInfoVO> prodTaskEqInfoList;

    @ApiModelProperty("附件信息")
    private List<ProductionTaskAttachmentsVO> prodTaskAttachmentList;

    @ApiModelProperty("操作历史")
    private List<ProdTaskOpHistVO> prodTaskOpHistDTOS;

    @ApiModelProperty("设备原始数据")
    private List<AttachmentVO> attachmentVOList;

    @ApiModelProperty("所属部门名字")
    private String departmentName;

    @ApiModelProperty("所属班组名字")
    private String belongingTeam2Name;
    
    @ApiModelProperty("委托单位名字")
    private String entrustedUnitName;
}