package com.huatek.frame.modules.business.service;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.business.domain.EvaluationOrderNotify;
import com.huatek.frame.modules.business.domain.vo.EvaluationOrderNotifyVO;
import com.huatek.frame.modules.business.service.dto.EvaluationOrderNotifyDTO;

import java.util.List;

/**
 * 测评订单通知服务层
 */
public interface EvaluationOrderNotifyService extends IService<EvaluationOrderNotify> {
    /**
     * 分页查询测评订单通知信息列表
     * @param requestParam
     * @return
     */
    TorchResponse<List<EvaluationOrderNotifyVO>> findAll(EvaluationOrderNotifyDTO requestParam);
}
