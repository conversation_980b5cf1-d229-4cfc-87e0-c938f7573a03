package com.huatek.frame.modules.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.huatek.frame.modules.business.domain.ProductList;
import com.huatek.frame.modules.business.service.dto.EvaluationOrderNotifyDTO;
import com.huatek.frame.modules.business.service.dto.ProductBatchUpdateReqDTO;
import com.huatek.frame.modules.business.service.dto.ProductListDTO;
import com.huatek.frame.modules.business.domain.vo.ProductListVO;
import com.huatek.frame.common.response.TorchResponse;
import java.util.List;


/**
* @description 产品列表Service
* <AUTHOR>
* @date 2025-07-30
**/
public interface ProductListService extends IService<ProductList> {
    
    /**
	 * 分页查找查找 产品列表
	 * 
	 * @param dto 产品列表dto实体对象
	 * @return 
	 */
	TorchResponse<List<ProductListVO>> findProductListPage(ProductListDTO dto);

    /**
	 * 添加 \修改 产品列表
	 * 
	 * @param productList1Dto 产品列表dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(ProductListDTO productList1Dto);
	
	/**
	 * 通过id查找产品列表
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<ProductListVO> findProductList(String id);
	
	/**
	 * 删除 产品列表
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 查找关联信息 产品列表
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<ProductListVO>> getOptionsList(String id);




    /**
     * 根据条件查询产品列表列表
     *
     * @param dto 产品列表信息
     * @return 产品列表集合信息
     */
    List<ProductListVO> selectProductDetails(ProductListDTO dto);

    /**
     * 导入产品列表数据
     *
     * @param productListList 产品列表数据列表
     * @param evaluationOrderId 该产品列表数据列表所属的测评订单ID

     * @return 结果
     */
    TorchResponse importProductList(List<ProductListVO> productListList, String evaluationOrderId);

	/**
	 * 根据IDS获取产品列表数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectProductListListByIds(List<String> ids);


	/**
	 * 批量修改产品列表字段值
	 * @param requestParam 批量修改产品列表请求参数
	 * @return
	 */
	TorchResponse batchUpdateProductList(ProductBatchUpdateReqDTO requestParam);

	/**
	 * 产品列表信息变更通知
	 * @param message
	 * @return
	 */
	TorchResponse notifyProductListChangeMessage(EvaluationOrderNotifyDTO requestParam);

	/**
	 * 根据测评订单id获取该订单下所有产品列表信息
	 * @param evaluationOrderId 测评订单id
	 * @return
	 */
	TorchResponse<List<ProductListVO>> selectProductListsByEvaluationOrderId(String evaluationOrderId);
}