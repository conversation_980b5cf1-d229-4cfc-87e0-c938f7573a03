package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.modules.business.domain.BaseEntity;
import com.huatek.frame.common.utils.HuatekTools;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import java.sql.Timestamp;
import java.sql.Date;
import java.io.Serializable;

/**
* @description 客户试验项目数据DTO 实体类
* <AUTHOR>
* @date 2025-07-17
**/
@Data
@ApiModel("客户试验项目数据DTO实体类")
public class CustomerExperimentProjectDataDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private String id;


    /**
     * 工序编码
     **/
    @ApiModelProperty("工序编码")
    private String standardProcessCode;


    /**
     * 参数名称
     **/
    @ApiModelProperty("参数名称")
    private String parameterName;


    /**
     * 参数单位
     **/
    @ApiModelProperty("参数单位")
    private String parameterUnit;


    /**
     * 规范值
     **/
    @ApiModelProperty("规范值")
    private String specificationValue;
    /**
	 * 主表单ID
     **/
    @ApiModelProperty("主表单ID")
    private String codexTorchMasterFormId;
    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;



	/**
	 * 页码
	 */
	@ApiModelProperty("当前页码")
	private Integer page;
	
	/**
	 * 每页显示数量
	 */
	@ApiModelProperty("每页显示大小")
	private Integer limit;

}