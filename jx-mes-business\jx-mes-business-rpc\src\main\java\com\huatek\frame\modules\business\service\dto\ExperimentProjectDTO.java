package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.modules.system.domain.BaseEntity;
import com.huatek.frame.common.utils.HuatekTools;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import java.sql.Timestamp;
import java.sql.Date;
import java.io.Serializable;

/**
* @description 试验项目DTO 实体类
* <AUTHOR>
* @date 2025-07-17
**/
@Data
@ApiModel("试验项目DTO实体类")
public class ExperimentProjectDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;
    
    /**
	 * 工序方案名称
     **/
    @ApiModelProperty("工序方案名称")
    private String processSchemeName;
    
    /**
	 * 显示序号
     **/
    @ApiModelProperty("显示序号")
    private Long displayNumber;

    /**
     * 工序Id
     */
    @ApiModelProperty("工序Id")
    private String processId;
    /**
	 * 工序编码
     **/
    @ApiModelProperty("工序编码")
    private String processCode3;
    
    /**
	 * 客户试验项目名称
     **/
    @ApiModelProperty("客户工序名称")
    private String customerProcessName;
    
    /**
	 * 执行顺序
     **/
    @ApiModelProperty("执行顺序")
    private Long executionSequence;
    
    /**
	 * 关联工单前置工序
     **/
    @ApiModelProperty("关联工单前置工序")
    private String assoWoPredProc;
    
    /**
	 * 试验条件
     **/
    @ApiModelProperty("试验条件")
    private String testConditions;

    /**
     * 试验依据
     **/
    @ApiModelProperty("试验依据")
    private String testBasis;
    
    /**
	 * 试验次数
     **/
    @ApiModelProperty("试验次数")
    private String testingTimes;
    
    /**
	 * 试验时长
     **/
    @ApiModelProperty("试验时长")
    private Long durationOfTesting;
    
    /**
	 * 判定依据
     **/
    @ApiModelProperty("判定依据")
    private String judgmentCriteria;
    
    /**
	 * 工作站
     **/
    @ApiModelProperty("工作站")
    private String workstation;
    
    /**
	 * 设备类型
     **/
    @ApiModelProperty("设备类型")
    private String deviceType;
    
    /**
	 * 器件资料
     **/
    @ApiModelProperty("产品资料")
    private String productInformation1;
    
    /**
	 * 试验方式
     **/
    @ApiModelProperty("试验方式")
    private String testMethodology;
    
    /**
	 * 试验班组
     **/
    @ApiModelProperty("试验班组")
    private String testingTeam;
    
    /**
	 * PDA
     **/
    @ApiModelProperty("PDA")
    private Long pda;
    
    /**
	 * 组别
     **/
    @ApiModelProperty("组别")
    private String grouping;
    
    /**
	 * 样品数量
     **/
    @ApiModelProperty("样品数量")
    private String sampleQuantity12;
    
    /**
	 * 是否加入排产
     **/
    @ApiModelProperty("是否加入排产")
    private String whetherToIncludeInScheduling;
    
    /**
	 * 主表单ID
     **/
    @ApiModelProperty("主表单ID")
    private String codexTorchMasterFormId;
    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;

    /**
     * 数据项细项
     */
    @ApiModelProperty("数据明细项")
    private ExperimentProjectDataDTO[] projectDataItems;

	/**
	 * 页码
	 */
	@ApiModelProperty("当前页码")
	private Integer page;
	
	/**
	 * 每页显示数量
	 */
	@ApiModelProperty("每页显示大小")
	private Integer limit;

}