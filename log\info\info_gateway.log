2025-08-20 10:19:21,347 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-08-20 10:19:21,369 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-08-20 10:19:21,426 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-08-20 10:19:41,460 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/logout
2025-08-20 10:19:42,242 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/code
2025-08-20 10:19:46,034 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/login
2025-08-20 10:19:47,094 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/login
2025-08-20 10:19:47,201 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/code
2025-08-20 10:19:51,546 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/login
2025-08-20 10:20:06,046 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/code
2025-08-20 10:20:11,546 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/code
